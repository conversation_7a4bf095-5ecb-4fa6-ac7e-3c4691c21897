// 安全配置
export const SECURITY_CONFIG = {
  // 允许的域名
  ALLOWED_DOMAINS: [
    'watermarkremover.top',
    'localhost:3000',
  ],
  
  // 允许的第三方域名
  TRUSTED_DOMAINS: [
    'www.googletagmanager.com',
    'www.google-analytics.com',
    'accounts.google.com',
    'oauth2.googleapis.com',
    'www.googleapis.com',
    'ssl.gstatic.com',
    'www.paypal.com',
    'js.paypal.com',
    'fonts.googleapis.com',
    'fonts.gstatic.com',
    '*.supabase.co',
    'api.textin.com',
    'api.klingai.com',
  ],
  
  // CSP配置 - 开发环境使用宽松配置
  CSP_DIRECTIVES: {
    'default-src': ["'self'"],
    'script-src': [
      "'self'",
      "'unsafe-inline'",
      "'unsafe-eval'",
      'https://www.googletagmanager.com',
      'https://www.google-analytics.com',
      'https://accounts.google.com',
      'https://oauth2.googleapis.com',
      'https://www.googleapis.com',
      'https://ssl.gstatic.com',
      'https://www.paypal.com',
      'https://js.paypal.com',
    ],
    'style-src': [
      "'self'",
      "'unsafe-inline'",
      'https://fonts.googleapis.com',
    ],
    'font-src': [
      "'self'",
      'https://fonts.gstatic.com',
    ],
    'img-src': [
      "'self'",
      'data:',
      'https:',
      'blob:',
    ],
    'connect-src': [
      "'self'",
      'https://www.google-analytics.com',
      'https://accounts.google.com',
      'https://oauth2.googleapis.com',
      'https://www.googleapis.com',
      'https://ssl.gstatic.com',
      'https://api.paypal.com',
      'https://www.paypal.com',
      'https://*.supabase.co',
      'wss://*.supabase.co',
      'https://api.textin.com',
      'https://api.klingai.com',
    ],
    'frame-src': [
      "'self'",
      'https://accounts.google.com',
      'https://oauth2.googleapis.com',
      'https://www.googleapis.com',
      'https://www.paypal.com',
      'https://js.paypal.com',
    ],
    'object-src': ["'none'"],
    'base-uri': ["'self'"],
    'form-action': ["'self'"],
    'frame-ancestors': ["'none'"],
    'upgrade-insecure-requests': [],
  },
  
  // 安全头配置
  SECURITY_HEADERS: {
    'X-Frame-Options': 'DENY',
    'X-Content-Type-Options': 'nosniff',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
    'Strict-Transport-Security': 'max-age=********; includeSubDomains; preload',
  },
};

// 生成随机nonce
export function generateNonce(): string {
  const array = new Uint8Array(16);
  crypto.getRandomValues(array);
  return Buffer.from(array).toString('base64');
}

// 生成CSP字符串（不带nonce）
export function generateCSP(): string {
  return Object.entries(SECURITY_CONFIG.CSP_DIRECTIVES)
    .map(([directive, sources]) => {
      if (sources.length === 0) return directive;
      return `${directive} ${sources.join(' ')}`;
    })
    .join('; ');
}

// 生成带nonce的CSP字符串
export function generateCSPWithNonce(nonce: string): string {
  return Object.entries(SECURITY_CONFIG.CSP_DIRECTIVES)
    .map(([directive, sources]) => {
      if (sources.length === 0) return directive;

      // 为script-src和style-src添加nonce
      if (directive === 'script-src' || directive === 'style-src') {
        const sourcesWithNonce = [...sources, `'nonce-${nonce}'`];
        return `${directive} ${sourcesWithNonce.join(' ')}`;
      }

      return `${directive} ${sources.join(' ')}`;
    })
    .join('; ');
}