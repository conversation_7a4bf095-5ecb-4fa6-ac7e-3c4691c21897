"use client";

import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";

export default function SignIn() {
  const t = useTranslations();

  const handleSignIn = () => {
    // 获取当前语言前缀
    const currentPath = window.location.pathname;
    const locale = currentPath.split('/')[1] || 'zh'; // 默认中文
    window.location.href = `/${locale}/auth/signin?callbackUrl=` + encodeURIComponent(currentPath);
  };

  return (
    <Button
      variant="default"
      onClick={handleSignIn}
      className="cursor-pointer"
    >
      {t("user.sign_in")}
    </Button>
  );
}
