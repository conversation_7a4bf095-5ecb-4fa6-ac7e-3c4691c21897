"use client";

import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import { signIn } from "next-auth/react";

export default function SignIn() {
  const t = useTranslations();

  const handleSignIn = () => {
    // 直接使用NextAuth的标准登录页面
    signIn(undefined, {
      callbackUrl: window.location.pathname,
      redirect: true
    });
  };

  return (
    <Button
      variant="default"
      onClick={handleSignIn}
      className="cursor-pointer"
    >
      {t("user.sign_in")}
    </Button>
  );
}
