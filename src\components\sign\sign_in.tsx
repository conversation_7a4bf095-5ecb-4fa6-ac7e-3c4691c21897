"use client";

import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import { signIn } from "next-auth/react";

export default function SignIn() {
  const t = useTranslations();

  const handleSignIn = () => {
    // 跳转到我们的标准登录页面
    const currentPath = window.location.pathname;
    const locale = currentPath.split('/')[1] || 'zh'; // 默认中文
    window.location.href = `/${locale}/auth/signin?callbackUrl=${encodeURIComponent(currentPath)}`;
  };

  return (
    <Button
      variant="default"
      onClick={handleSignIn}
      className="cursor-pointer"
    >
      {t("user.sign_in")}
    </Button>
  );
}
