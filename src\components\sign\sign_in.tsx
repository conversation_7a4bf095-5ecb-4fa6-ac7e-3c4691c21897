"use client";

import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";

export default function SignIn() {
  const t = useTranslations();

  const handleSignIn = () => {
    window.location.href = '/auth/signin?callbackUrl=' + encodeURIComponent(window.location.pathname);
  };

  return (
    <Button
      variant="default"
      onClick={handleSignIn}
      className="cursor-pointer"
    >
      {t("user.sign_in")}
    </Button>
  );
}
