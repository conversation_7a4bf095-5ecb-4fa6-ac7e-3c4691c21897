# 安全修复部署检查清单

## 🚀 部署前检查

### 1. 代码修改确认
- [x] ✅ 更新 `src/config/security.ts` 中的 CSP 配置
- [x] ✅ 更新 `next.config.mjs` 中的 CSP 配置
- [x] ✅ 添加 `api.textin.com` 到信任域名列表
- [x] ✅ 添加 `api.klingai.com` 到信任域名列表
- [x] ✅ 添加 Google OAuth 域名到 CSP 配置
  - [x] ✅ `oauth2.googleapis.com`
  - [x] ✅ `www.googleapis.com`
  - [x] ✅ `ssl.gstatic.com`
- [x] ✅ 创建安全检查脚本
- [x] ✅ 创建OAuth安全检查脚本
- [x] ✅ 创建修复文档

### 2. 本地测试
- [x] ✅ 运行安全检查脚本
- [x] ✅ 运行OAuth安全检查脚本
- [ ] ⏳ 本地测试去水印功能
- [ ] ⏳ 本地测试登录功能
- [ ] ⏳ 验证CSP配置生效

## 🌐 部署步骤

### 1. 提交代码
```bash
git add .
git commit -m "fix: 修复CSP配置，添加外部API和OAuth域名到安全策略

- 添加 api.textin.com 和 api.klingai.com 到 CSP
- 添加 Google OAuth 域名到 CSP 配置
- 修复登录时出现安全警告的问题
- 创建安全检查和OAuth检查脚本"
git push origin main
```

### 2. 部署到生产环境
- [ ] ⏳ 触发生产环境部署
- [ ] ⏳ 等待部署完成
- [ ] ⏳ 验证部署状态

### 3. 部署后验证
```bash
# 运行安全检查
node scripts/security-check.js

# 运行OAuth安全检查
node scripts/oauth-security-check.js

# 检查特定API域名
curl -I https://watermarkremover.top | grep -i content-security-policy
```

## 🔍 验证清单

### 1. CSP 配置验证
- [ ] ⏳ `api.textin.com` 在 connect-src 中
- [ ] ⏳ `api.klingai.com` 在 connect-src 中
- [ ] ⏳ 其他安全头正确设置

### 2. 功能测试
- [ ] ⏳ 去水印功能正常工作
- [ ] ⏳ 无CSP违规错误
- [ ] ⏳ 浏览器控制台无安全警告

### 3. 安全状态检查
- [ ] ⏳ 网站可正常访问
- [ ] ⏳ 无浏览器安全警告
- [ ] ⏳ 谷歌安全浏览状态

## 📋 谷歌安全浏览审查

### 1. 等待自动重新爬取
- 谷歌通常在24-48小时内重新爬取网站
- 如果CSP问题已修复，状态应该自动更新

### 2. 手动提交审查请求
如果48小时后状态仍未更新：

1. **访问谷歌安全浏览报告页面**:
   https://safebrowsing.google.com/safebrowsing/report_general/

2. **填写审查请求**:
   - 网站URL: `https://watermarkremover.top`
   - 问题类型: "我的网站被错误标记"
   - 说明: "网站CSP配置已修复，请重新审查"

3. **提供修复证据**:
   - 安全检查脚本输出
   - CSP配置截图
   - 功能正常工作的证明

### 3. 监控状态变化
- 使用谷歌搜索控制台监控
- 定期检查安全浏览状态
- 设置监控报警

## 🛠️ 故障排除

### 如果部署后仍有问题

1. **检查CSP是否生效**:
```bash
curl -I https://watermarkremover.top | grep -i content-security-policy
```

2. **检查浏览器控制台**:
   - 打开开发者工具
   - 查看Console和Network标签
   - 寻找CSP违规错误

3. **验证API调用**:
   - 测试去水印功能
   - 检查网络请求是否被阻止
   - 确认API响应正常

### 常见问题

1. **CSP缓存问题**:
   - 清除浏览器缓存
   - 使用无痕模式测试
   - 等待CDN缓存更新

2. **配置不一致**:
   - 检查所有CSP配置位置
   - 确保middleware正确应用
   - 验证环境变量设置

3. **部署延迟**:
   - 确认代码已正确部署
   - 检查构建日志
   - 验证版本号

## 📊 成功指标

### 技术指标
- [ ] ⏳ CSP包含所有必要域名
- [ ] ⏳ 无CSP违规错误
- [ ] ⏳ 所有功能正常工作
- [ ] ⏳ 安全头配置正确

### 业务指标
- [ ] ⏳ 网站可正常访问
- [ ] ⏳ 用户无安全警告
- [ ] ⏳ 谷歌搜索结果正常
- [ ] ⏳ 用户体验恢复正常

## 📞 联系信息

如果遇到问题，请联系：
- 技术支持: [技术团队邮箱]
- 紧急联系: [紧急联系方式]

---

**注意**: 此清单应在每次安全相关部署时使用，确保所有步骤都得到正确执行。
