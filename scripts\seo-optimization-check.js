#!/usr/bin/env node

/**
 * SEO优化检查脚本
 * 全面检查网站的SEO配置和优化状态
 */

const https = require('https');
const http = require('http');

const SITE_URL = process.env.NEXT_PUBLIC_WEB_URL || 'https://watermarkremover.top';

console.log('🔍 开始SEO优化检查...');
console.log(`检查网站: ${SITE_URL}`);

// 1. 检查Sitemap
function checkSitemap(siteUrl) {
  return new Promise((resolve) => {
    console.log('\n🗺️ Sitemap检查:');
    
    const sitemapUrl = `${siteUrl}/sitemap.xml`;
    const protocol = sitemapUrl.startsWith('https') ? https : http;
    
    const req = protocol.get(sitemapUrl, (res) => {
      console.log(`📊 Sitemap状态码: ${res.statusCode}`);
      console.log(`📋 Content-Type: ${res.headers['content-type']}`);
      
      if (res.statusCode === 200) {
        console.log('✅ Sitemap可访问');
        
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          // 检查sitemap内容
          const urlCount = (data.match(/<url>/g) || []).length;
          const locCount = (data.match(/<loc>/g) || []).length;
          const lastmodCount = (data.match(/<lastmod>/g) || []).length;
          const priorityCount = (data.match(/<priority>/g) || []).length;
          
          console.log(`📍 URL数量: ${urlCount}`);
          console.log(`🔗 Loc标签: ${locCount}`);
          console.log(`📅 LastMod标签: ${lastmodCount}`);
          console.log(`⭐ Priority标签: ${priorityCount}`);
          
          if (urlCount > 0) {
            console.log('✅ Sitemap包含URL');
          } else {
            console.log('❌ Sitemap为空');
          }
          
          resolve({ urlCount, locCount, lastmodCount, priorityCount });
        });
      } else {
        console.log('❌ Sitemap不可访问');
        resolve({ urlCount: 0, locCount: 0, lastmodCount: 0, priorityCount: 0 });
      }
    });
    
    req.on('error', (err) => {
      console.log(`❌ Sitemap检查失败: ${err.message}`);
      resolve({ urlCount: 0, locCount: 0, lastmodCount: 0, priorityCount: 0 });
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      console.log('❌ Sitemap检查超时');
      resolve({ urlCount: 0, locCount: 0, lastmodCount: 0, priorityCount: 0 });
    });
  });
}

// 2. 检查结构化数据
function checkStructuredData(siteUrl) {
  return new Promise((resolve) => {
    console.log('\n📊 结构化数据检查:');
    
    const protocol = siteUrl.startsWith('https') ? https : http;
    
    const req = protocol.get(siteUrl, (res) => {
      let html = '';
      res.on('data', chunk => html += chunk);
      res.on('end', () => {
        const schemas = [];
        
        // 检查JSON-LD结构化数据
        const jsonLdMatches = html.match(/<script[^>]*type\s*=\s*["']application\/ld\+json["'][^>]*>(.*?)<\/script>/gis);
        
        if (jsonLdMatches) {
          jsonLdMatches.forEach((match, index) => {
            try {
              const jsonContent = match.replace(/<script[^>]*>/, '').replace(/<\/script>/, '');
              const data = JSON.parse(jsonContent);
              schemas.push(data['@type'] || 'Unknown');
              console.log(`✅ Schema ${index + 1}: ${data['@type'] || 'Unknown'}`);
            } catch (e) {
              console.log(`❌ Schema ${index + 1}: 解析失败`);
            }
          });
        }
        
        // 检查特定的结构化数据类型
        const expectedSchemas = ['Organization', 'WebSite', 'Service', 'FAQPage'];
        const foundSchemas = [];
        
        expectedSchemas.forEach(schema => {
          if (schemas.includes(schema)) {
            foundSchemas.push(schema);
            console.log(`✅ ${schema} Schema 已实现`);
          } else {
            console.log(`❌ ${schema} Schema 缺失`);
          }
        });
        
        console.log(`\n📊 结构化数据总数: ${schemas.length}`);
        console.log(`✅ 已实现的Schema: ${foundSchemas.length}/${expectedSchemas.length}`);
        
        resolve({ total: schemas.length, found: foundSchemas.length, expected: expectedSchemas.length });
      });
    });
    
    req.on('error', (err) => {
      console.log(`❌ 结构化数据检查失败: ${err.message}`);
      resolve({ total: 0, found: 0, expected: 4 });
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      console.log('❌ 结构化数据检查超时');
      resolve({ total: 0, found: 0, expected: 4 });
    });
  });
}

// 3. 检查Meta标签
function checkMetaTags(siteUrl) {
  return new Promise((resolve) => {
    console.log('\n🏷️ Meta标签检查:');
    
    const protocol = siteUrl.startsWith('https') ? https : http;
    
    const req = protocol.get(siteUrl, (res) => {
      let html = '';
      res.on('data', chunk => html += chunk);
      res.on('end', () => {
        const metaChecks = {
          title: false,
          description: false,
          keywords: false,
          ogTitle: false,
          ogDescription: false,
          ogImage: false,
          twitterCard: false,
          canonical: false,
          viewport: false,
          robots: false
        };
        
        // 检查各种Meta标签
        if (html.includes('<title>') && !html.includes('<title></title>')) {
          metaChecks.title = true;
          console.log('✅ Title标签存在');
        } else {
          console.log('❌ Title标签缺失或为空');
        }
        
        if (html.match(/<meta[^>]*name\s*=\s*["']description["'][^>]*content\s*=\s*["'][^"']+["']/i)) {
          metaChecks.description = true;
          console.log('✅ Description Meta标签存在');
        } else {
          console.log('❌ Description Meta标签缺失');
        }
        
        if (html.match(/<meta[^>]*name\s*=\s*["']keywords["'][^>]*content\s*=\s*["'][^"']+["']/i)) {
          metaChecks.keywords = true;
          console.log('✅ Keywords Meta标签存在');
        } else {
          console.log('❌ Keywords Meta标签缺失');
        }
        
        if (html.match(/<meta[^>]*property\s*=\s*["']og:title["'][^>]*content\s*=\s*["'][^"']+["']/i)) {
          metaChecks.ogTitle = true;
          console.log('✅ OG:Title标签存在');
        } else {
          console.log('❌ OG:Title标签缺失');
        }
        
        if (html.match(/<meta[^>]*property\s*=\s*["']og:description["'][^>]*content\s*=\s*["'][^"']+["']/i)) {
          metaChecks.ogDescription = true;
          console.log('✅ OG:Description标签存在');
        } else {
          console.log('❌ OG:Description标签缺失');
        }
        
        if (html.match(/<meta[^>]*property\s*=\s*["']og:image["'][^>]*content\s*=\s*["'][^"']+["']/i)) {
          metaChecks.ogImage = true;
          console.log('✅ OG:Image标签存在');
        } else {
          console.log('❌ OG:Image标签缺失');
        }
        
        if (html.match(/<meta[^>]*name\s*=\s*["']twitter:card["'][^>]*content\s*=\s*["'][^"']+["']/i)) {
          metaChecks.twitterCard = true;
          console.log('✅ Twitter Card标签存在');
        } else {
          console.log('❌ Twitter Card标签缺失');
        }
        
        if (html.match(/<link[^>]*rel\s*=\s*["']canonical["'][^>]*href\s*=\s*["'][^"']+["']/i)) {
          metaChecks.canonical = true;
          console.log('✅ Canonical链接存在');
        } else {
          console.log('❌ Canonical链接缺失');
        }
        
        if (html.match(/<meta[^>]*name\s*=\s*["']viewport["'][^>]*content\s*=\s*["'][^"']+["']/i)) {
          metaChecks.viewport = true;
          console.log('✅ Viewport Meta标签存在');
        } else {
          console.log('❌ Viewport Meta标签缺失');
        }
        
        if (html.match(/<meta[^>]*name\s*=\s*["']robots["'][^>]*content\s*=\s*["'][^"']+["']/i)) {
          metaChecks.robots = true;
          console.log('✅ Robots Meta标签存在');
        } else {
          console.log('❌ Robots Meta标签缺失');
        }
        
        const passedChecks = Object.values(metaChecks).filter(Boolean).length;
        const totalChecks = Object.keys(metaChecks).length;
        
        console.log(`\n📊 Meta标签评分: ${passedChecks}/${totalChecks}`);
        
        resolve({ passed: passedChecks, total: totalChecks, checks: metaChecks });
      });
    });
    
    req.on('error', (err) => {
      console.log(`❌ Meta标签检查失败: ${err.message}`);
      resolve({ passed: 0, total: 10, checks: {} });
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      console.log('❌ Meta标签检查超时');
      resolve({ passed: 0, total: 10, checks: {} });
    });
  });
}

// 4. 检查多语言支持
function checkMultiLanguage(siteUrl) {
  return new Promise((resolve) => {
    console.log('\n🌍 多语言支持检查:');
    
    const languages = ['en', 'zh', 'fr', 'pt', 'ru'];
    const results = [];
    
    const checkLanguage = (lang) => {
      return new Promise((resolveLang) => {
        const url = lang === 'en' ? siteUrl : `${siteUrl}/${lang}`;
        const protocol = url.startsWith('https') ? https : http;
        
        const req = protocol.get(url, (res) => {
          console.log(`📍 ${lang}: ${res.statusCode} ${res.statusCode === 200 ? '✅' : '❌'}`);
          resolveLang(res.statusCode === 200);
        });
        
        req.on('error', () => {
          console.log(`📍 ${lang}: 连接失败 ❌`);
          resolveLang(false);
        });
        
        req.setTimeout(5000, () => {
          req.destroy();
          console.log(`📍 ${lang}: 超时 ❌`);
          resolveLang(false);
        });
      });
    };
    
    Promise.all(languages.map(checkLanguage)).then(results => {
      const workingLanguages = results.filter(Boolean).length;
      console.log(`\n📊 多语言支持: ${workingLanguages}/${languages.length}`);
      resolve({ working: workingLanguages, total: languages.length });
    });
  });
}

// 5. 生成SEO报告
function generateSEOReport(sitemapData, schemaData, metaData, langData) {
  console.log('\n📋 SEO优化报告:');
  console.log('=' * 50);
  
  let score = 0;
  const maxScore = 100;
  
  // Sitemap评分 (20分)
  if (sitemapData.urlCount > 0) {
    score += 15;
    if (sitemapData.lastmodCount > 0) score += 3;
    if (sitemapData.priorityCount > 0) score += 2;
  }
  
  // 结构化数据评分 (25分)
  const schemaScore = (schemaData.found / schemaData.expected) * 25;
  score += schemaScore;
  
  // Meta标签评分 (30分)
  const metaScore = (metaData.passed / metaData.total) * 30;
  score += metaScore;
  
  // 多语言支持评分 (25分)
  const langScore = (langData.working / langData.total) * 25;
  score += langScore;
  
  score = Math.round(score);
  
  console.log(`\n📊 SEO总评分: ${score}/${maxScore}`);
  
  if (score >= 90) {
    console.log('🏆 SEO优化优秀');
  } else if (score >= 75) {
    console.log('✅ SEO优化良好');
  } else if (score >= 60) {
    console.log('⚠️ SEO优化一般，需要改进');
  } else {
    console.log('❌ SEO优化不足，需要大幅改进');
  }
  
  console.log('\n📊 详细评分:');
  console.log(`🗺️ Sitemap: ${sitemapData.urlCount > 0 ? '✅' : '❌'} (${Math.min(20, sitemapData.urlCount > 0 ? 15 + (sitemapData.lastmodCount > 0 ? 3 : 0) + (sitemapData.priorityCount > 0 ? 2 : 0) : 0)}/20)`);
  console.log(`📊 结构化数据: ${schemaData.found}/${schemaData.expected} (${Math.round(schemaScore)}/25)`);
  console.log(`🏷️ Meta标签: ${metaData.passed}/${metaData.total} (${Math.round(metaScore)}/30)`);
  console.log(`🌍 多语言: ${langData.working}/${langData.total} (${Math.round(langScore)}/25)`);
  
  console.log('\n💡 优化建议:');
  if (sitemapData.urlCount === 0) {
    console.log('1. 修复Sitemap生成问题');
  }
  if (schemaData.found < schemaData.expected) {
    console.log('2. 添加缺失的结构化数据');
  }
  if (metaData.passed < metaData.total) {
    console.log('3. 完善Meta标签配置');
  }
  if (langData.working < langData.total) {
    console.log('4. 修复多语言页面访问问题');
  }
  
  return score;
}

// 主函数
async function main() {
  try {
    const sitemapData = await checkSitemap(SITE_URL);
    const schemaData = await checkStructuredData(SITE_URL);
    const metaData = await checkMetaTags(SITE_URL);
    const langData = await checkMultiLanguage(SITE_URL);
    
    const score = generateSEOReport(sitemapData, schemaData, metaData, langData);
    
    console.log('\n🚀 下一步行动:');
    if (score >= 85) {
      console.log('1. SEO优化良好，继续监控');
      console.log('2. 定期更新内容和sitemap');
    } else {
      console.log('1. 根据上述建议进行优化');
      console.log('2. 重新运行SEO检查验证改进');
      console.log('3. 考虑添加更多高质量内容');
    }
    
    process.exit(score < 60 ? 1 : 0);
    
  } catch (error) {
    console.error('❌ SEO检查失败:', error.message);
    process.exit(1);
  }
}

main();
