import {
  getMessages,
  getTranslations,
  setRequestLocale,
} from "next-intl/server";
import { AppContextProvider } from "@/contexts/app";
import { Metadata } from "next";
import { NextAuthSessionProvider } from "@/auth/session";
import { NextIntlClientProvider } from "next-intl";
import { ThemeProvider } from "@/providers/theme";
import { Toaster } from "@/components/ui/toaster";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  setRequestLocale(locale);

  const t = await getTranslations();
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://watermarkremover.top';
  const localePrefix = locale === 'en' ? '' : `/${locale}`;
  const canonicalUrl = `${baseUrl}${localePrefix}`;

  return {
    title: {
      template: `%s | ${t("metadata.title")}`,
      default: t("metadata.title") || "",
    },
    description: t("metadata.description") || "",
    keywords: t("metadata.keywords") || "",
    authors: [{ name: t("metadata.title") }],
    creator: t("metadata.title"),
    publisher: t("metadata.title"),
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    metadataBase: new URL(baseUrl),
    alternates: {
      canonical: canonicalUrl,
      languages: {
        'en': `${baseUrl}`,
        'zh': `${baseUrl}/zh`,
        'fr': `${baseUrl}/fr`,
        'pt': `${baseUrl}/pt`,
        'ru': `${baseUrl}/ru`,
        'x-default': `${baseUrl}`,
      },
    },
    openGraph: {
      type: 'website',
      locale: locale,
      url: canonicalUrl,
      title: t("metadata.title"),
      description: t("metadata.description"),
      siteName: t("metadata.title"),
      images: [
        {
          url: `${baseUrl}/logo.png`,
          width: 1200,
          height: 630,
          alt: t("metadata.title"),
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: t("metadata.title"),
      description: t("metadata.description"),
      images: [`${baseUrl}/logo.png`],
      creator: '@watermarkremover',
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    icons: {
      icon: '/logo.png',
      shortcut: '/logo.png',
      apple: '/logo.png',
    },
    manifest: '/manifest.json',
  };
}

export default async function LocaleLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}>) {
  const { locale } = await params;
  setRequestLocale(locale);

  const messages = await getMessages();

  return (
    <NextIntlClientProvider messages={messages}>
      <NextAuthSessionProvider>
        <AppContextProvider>
          <ThemeProvider attribute="class">
            {children}
            <Toaster />
          </ThemeProvider>
        </AppContextProvider>
      </NextAuthSessionProvider>
    </NextIntlClientProvider>
  );
}
