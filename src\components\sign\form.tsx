"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { SiGoogle } from "react-icons/si";
import { Button } from "@/components/ui/button";
import { signIn } from "next-auth/react";
import { useTranslations } from "next-intl";
import { Link } from "@/i18n/navigation";

// 简洁的登录表单组件
export default function SignForm({ className, ...props }: React.ComponentPropsWithoutRef<"div">) {
  const t = useTranslations();

  // 获取回调URL
  const getCallbackUrl = () => {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      return urlParams.get('callbackUrl') || '/';
    }
    return '/';
  };

  return (
    <div className={"flex flex-col gap-6 " + (className || "")} {...props}>
      <Card className="w-full">
        <CardHeader className="text-center pb-4">
          <CardTitle className="text-2xl font-semibold">{t('sign_modal.sign_in_title')}</CardTitle>
          <p className="text-sm text-muted-foreground">{t('sign_modal.sign_in_description')}</p>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button
            variant="outline"
            className="w-full flex items-center justify-center gap-3 py-6 text-base font-medium border-2 hover:bg-gray-50 transition-colors"
            onClick={() => signIn("google", {
              callbackUrl: getCallbackUrl(),
              redirect: true
            })}
          >
            <SiGoogle className="w-5 h-5" />
            {t('sign_modal.google_sign_in')}
          </Button>
        </CardContent>
      </Card>
      <div className="text-balance text-center text-xs text-muted-foreground [&_a]:underline [&_a]:underline-offset-4 hover:[&_a]:text-primary">
        {t('sign_modal.terms_agreement')} <Link href="/terms-of-service" target="_blank">{t('sign_modal.terms_of_service')}</Link> {t('sign_modal.and')} <Link href="/privacy-policy" target="_blank">{t('sign_modal.privacy_policy')}</Link>.
      </div>
    </div>
  );
}
