"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { SiGoogle } from "react-icons/si";
import { Button } from "@/components/ui/button";
import { signIn } from "next-auth/react";
import { useTranslations } from "next-intl";
import { Link } from "@/i18n/navigation";

// 登录表单组件，仅保留Google登录
export default function SignForm({ className, ...props }: React.ComponentPropsWithoutRef<"div">) {
  const t = useTranslations();
  return (
    <div className={"flex flex-col gap-6 " + (className || "")} {...props}>
      <Card>
        <CardHeader className="text-center">
          <CardTitle className="text-xl">{t('sign_modal.google_sign_in_title')}</CardTitle>
        </CardHeader>
        <CardContent>
          {/* 只保留Google登录按钮 */}
          <Button
            variant="outline"
            className="w-full flex items-center justify-center gap-2 text-base py-3 border-blue-400 hover:bg-blue-50 hover:border-blue-600 transition"
            onClick={() => signIn("google", {
              callbackUrl: "/",
              redirect: true
            })}
          >
            <SiGoogle className="w-5 h-5 text-blue-600" />
            {t('sign_modal.google_sign_in')}
          </Button>
        </CardContent>
      </Card>
      <div className="text-balance text-center text-xs text-muted-foreground [&_a]:underline [&_a]:underline-offset-4 hover:[&_a]:text-primary">
        {t('sign_modal.terms_agreement')} <Link href="/terms-of-service" target="_blank">{t('sign_modal.terms_of_service')}</Link> {t('sign_modal.and')} <Link href="/privacy-policy" target="_blank">{t('sign_modal.privacy_policy')}</Link>.
      </div>
    </div>
  );
}
