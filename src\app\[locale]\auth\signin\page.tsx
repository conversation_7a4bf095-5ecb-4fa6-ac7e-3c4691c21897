import SignForm from "@/components/sign/form";
import { auth } from "@/auth";
import { redirect } from "next/navigation";

export default async function SignInPage({
  searchParams,
}: {
  searchParams: Promise<{ callbackUrl: string | undefined }>;
}) {
  const { callbackUrl } = await searchParams;
  const session = await auth();
  if (session) {
    return redirect(callbackUrl || "/");
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-gray-50 p-6">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <a href="/" className="inline-flex items-center gap-2 text-xl font-semibold text-gray-900 hover:text-gray-700 transition-colors">
            <img src="/logo.png" alt="logo" className="h-8 w-8" />
            {process.env.NEXT_PUBLIC_PROJECT_NAME}
          </a>
        </div>
        <SignForm />
      </div>
    </div>
  );
}
