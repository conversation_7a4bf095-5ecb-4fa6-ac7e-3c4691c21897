import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { getTranslations } from "next-intl/server";
import SignInForm from "@/components/auth/signin-form";

export default async function SignInPage({
  searchParams,
}: {
  searchParams: Promise<{ callbackUrl: string | undefined }>;
}) {
  const { callbackUrl } = await searchParams;
  const session = await auth();
  const t = await getTranslations('auth');
  
  if (session) {
    return redirect(callbackUrl || "/");
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        {/* Logo and Title */}
        <div className="text-center">
          <a href="/" className="inline-flex items-center gap-2 text-2xl font-bold text-gray-900 hover:text-gray-700 transition-colors">
            <img src="/logo.png" alt="logo" className="h-10 w-10" />
            {process.env.NEXT_PUBLIC_PROJECT_NAME}
          </a>
          <h2 className="mt-6 text-3xl font-bold text-gray-900">
            {t('signin.title')}
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            {t('signin.subtitle')}
          </p>
        </div>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow-lg sm:rounded-lg sm:px-10 border border-gray-200">
          <SignInForm callbackUrl={callbackUrl} />
        </div>
        
        {/* Footer */}
        <div className="mt-6 text-center">
          <p className="text-xs text-gray-500">
            {t('signin.terms_notice')}
          </p>
        </div>
      </div>
    </div>
  );
}
