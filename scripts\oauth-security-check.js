#!/usr/bin/env node

/**
 * OAuth安全检查脚本
 * 专门检查OAuth认证流程中的安全配置
 */

const https = require('https');
const http = require('http');

const SITE_URL = process.env.NEXT_PUBLIC_WEB_URL || 'https://watermarkremover.top';

console.log('🔐 开始OAuth安全检查...');
console.log(`检查网站: ${SITE_URL}`);

// 检查OAuth相关的CSP配置
function checkOAuthCSP(url) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https') ? https : http;
    
    const req = protocol.get(url, (res) => {
      const csp = res.headers['content-security-policy'];
      
      console.log('\n🔒 OAuth CSP 检查结果:');
      if (csp) {
        console.log('✅ CSP 头存在');
        
        // 检查Google OAuth相关域名
        const googleOAuthDomains = [
          'accounts.google.com',
          'oauth2.googleapis.com',
          'www.googleapis.com',
          'ssl.gstatic.com'
        ];
        
        console.log('\n📋 Google OAuth 域名检查:');
        googleOAuthDomains.forEach(domain => {
          if (csp.includes(domain)) {
            console.log(`✅ ${domain} 已在 CSP 中允许`);
          } else {
            console.log(`❌ ${domain} 未在 CSP 中允许`);
          }
        });
        
        // 检查CSP指令
        const cspDirectives = {
          'script-src': '脚本源',
          'connect-src': '连接源',
          'frame-src': '框架源'
        };
        
        console.log('\n📝 CSP 指令检查:');
        Object.entries(cspDirectives).forEach(([directive, description]) => {
          if (csp.includes(directive)) {
            console.log(`✅ ${directive} (${description}) 已配置`);
          } else {
            console.log(`❌ ${directive} (${description}) 未配置`);
          }
        });
        
      } else {
        console.log('❌ 未找到 CSP 头');
      }
      
      resolve();
    });
    
    req.on('error', (err) => {
      console.error('❌ 请求失败:', err.message);
      reject(err);
    });
    
    req.setTimeout(10000, () => {
      console.error('❌ 请求超时');
      req.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

// 检查OAuth回调端点
function checkOAuthEndpoints(baseUrl) {
  return new Promise((resolve, reject) => {
    const authUrl = `${baseUrl}/api/auth/providers`;
    const protocol = authUrl.startsWith('https') ? https : http;
    
    const req = protocol.get(authUrl, (res) => {
      console.log('\n🔗 OAuth 端点检查:');
      
      if (res.statusCode === 200) {
        console.log('✅ OAuth providers 端点可访问');
        
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          try {
            const providers = JSON.parse(data);
            console.log('📋 可用的OAuth提供商:');
            Object.keys(providers).forEach(provider => {
              console.log(`  - ${provider}: ${providers[provider].name}`);
            });
          } catch (e) {
            console.log('⚠️ 无法解析providers响应');
          }
          resolve();
        });
      } else {
        console.log(`❌ OAuth providers 端点不可访问 (状态码: ${res.statusCode})`);
        resolve();
      }
    });
    
    req.on('error', (err) => {
      console.log(`❌ 无法访问 OAuth 端点: ${err.message}`);
      resolve();
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      console.log('❌ OAuth 端点请求超时');
      resolve();
    });
  });
}

// 检查HTTPS配置
function checkHTTPSConfig(url) {
  console.log('\n🔐 HTTPS 配置检查:');
  
  if (url.startsWith('https://')) {
    console.log('✅ 使用 HTTPS 协议');
    
    // 检查是否强制HTTPS
    const httpUrl = url.replace('https://', 'http://');
    return new Promise((resolve) => {
      const req = http.get(httpUrl, (res) => {
        if (res.statusCode >= 300 && res.statusCode < 400) {
          const location = res.headers.location;
          if (location && location.startsWith('https://')) {
            console.log('✅ HTTP 自动重定向到 HTTPS');
          } else {
            console.log('⚠️ HTTP 重定向但不是到 HTTPS');
          }
        } else {
          console.log('❌ HTTP 没有重定向到 HTTPS');
        }
        resolve();
      });
      
      req.on('error', () => {
        console.log('⚠️ 无法测试 HTTP 重定向');
        resolve();
      });
      
      req.setTimeout(5000, () => {
        req.destroy();
        console.log('⚠️ HTTP 重定向测试超时');
        resolve();
      });
    });
  } else {
    console.log('❌ 未使用 HTTPS 协议');
    return Promise.resolve();
  }
}

// 主函数
async function main() {
  try {
    await checkHTTPSConfig(SITE_URL);
    await checkOAuthCSP(SITE_URL);
    await checkOAuthEndpoints(SITE_URL);
    
    console.log('\n✅ OAuth安全检查完成!');
    console.log('\n💡 OAuth安全建议:');
    console.log('1. 确保所有Google OAuth域名都在CSP中正确配置');
    console.log('2. 使用HTTPS协议进行OAuth认证');
    console.log('3. 配置正确的OAuth回调URL');
    console.log('4. 定期检查OAuth提供商的安全更新');
    console.log('5. 监控OAuth认证流程中的异常');
    
    console.log('\n🔧 如果发现问题:');
    console.log('1. 更新CSP配置添加缺失的Google域名');
    console.log('2. 确保生产环境使用HTTPS');
    console.log('3. 检查Google OAuth应用配置');
    console.log('4. 验证回调URL设置');
    
  } catch (error) {
    console.error('❌ OAuth安全检查失败:', error.message);
    process.exit(1);
  }
}

main();
