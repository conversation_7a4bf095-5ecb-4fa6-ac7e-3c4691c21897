import { respData, respErr } from "@/lib/resp";
import { findContactById, markContactAsRead, replyToContact, closeContact, updateContact } from "@/models/contact";
import { isCurrentUserAdmin } from "@/services/admin";
import { getUserUuid } from "@/services/user";
import { findUserByUuid } from "@/models/user";

export async function GET(req: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    // 检查管理员权限
    const isAdmin = await isCurrentUserAdmin();
    if (!isAdmin) {
      return respErr("无权限访问", 403);
    }

    const { id: idParam } = await params;
    const id = parseInt(idParam);
    if (isNaN(id)) {
      return respErr("无效的ID");
    }

    const contact = await findContactById(id);
    if (!contact) {
      return respErr("联系记录不存在");
    }

    return respData(contact);
  } catch (e) {
    console.log("get contact failed: ", e);
    return respErr("获取联系记录失败");
  }
}

export async function PUT(req: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    // 检查管理员权限
    const isAdmin = await isCurrentUserAdmin();
    if (!isAdmin) {
      return respErr("无权限访问", 403);
    }

    const { id: idParam } = await params;
    const id = parseInt(idParam);
    if (isNaN(id)) {
      return respErr("无效的ID");
    }

    const { action, reply } = await req.json();

    let contact;
    const userUuid = await getUserUuid();
    const user = userUuid ? await findUserByUuid(userUuid) : null;
    const adminEmail = user?.email || "unknown";

    switch (action) {
      case "mark_read":
        contact = await markContactAsRead(id);
        break;
      case "reply":
        if (!reply || !reply.trim()) {
          return respErr("回复内容不能为空");
        }
        contact = await replyToContact(id, reply.trim(), adminEmail);
        break;
      case "close":
        contact = await closeContact(id);
        break;
      case "update_status":
        const { status } = await req.json();
        if (!status) {
          return respErr("状态不能为空");
        }
        contact = await updateContact(id, { status });
        break;
      default:
        return respErr("无效的操作");
    }

    if (!contact) {
      return respErr("操作失败");
    }

    return respData(contact);
  } catch (e) {
    console.log("update contact failed: ", e);
    return respErr("更新联系记录失败");
  }
}
