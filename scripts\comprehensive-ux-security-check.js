#!/usr/bin/env node

/**
 * 综合用户体验和安全检查脚本
 * 确保网站既安全又有良好的用户体验
 */

require('dotenv').config({ path: '.env.local' });
require('dotenv').config({ path: '.env.development' });
require('dotenv').config({ path: '.env' });

const https = require('https');
const http = require('http');
const fs = require('fs');

const SITE_URL = process.env.NEXT_PUBLIC_WEB_URL || 'http://localhost:3000';

console.log('🔍 综合安全和用户体验检查...');
console.log(`检查网站: ${SITE_URL}`);

// 1. 检查Google安全合规性
function checkGoogleCompliance(siteUrl) {
  return new Promise((resolve) => {
    console.log('\n🛡️ Google安全合规检查:');
    
    const protocol = siteUrl.startsWith('https') ? https : http;
    
    const req = protocol.get(siteUrl, (res) => {
      let html = '';
      res.on('data', chunk => html += chunk);
      res.on('end', () => {
        const compliance = {
          score: 0,
          issues: [],
          passed: []
        };
        
        // 检查1: 是否有自定义登录表单（钓鱼风险）
        const hasPasswordInput = html.includes('type="password"');
        const hasEmailInput = html.includes('type="email"');
        const hasCustomForm = hasPasswordInput && hasEmailInput;
        
        if (!hasCustomForm) {
          compliance.score += 25;
          compliance.passed.push('无自定义登录表单');
          console.log('✅ 无自定义登录表单（防钓鱼）');
        } else {
          compliance.issues.push('发现自定义登录表单');
          console.log('❌ 发现自定义登录表单');
        }
        
        // 检查2: CSP安全头部
        const csp = res.headers['content-security-policy'];
        if (csp && !csp.includes('unsafe-eval')) {
          compliance.score += 20;
          compliance.passed.push('CSP不包含unsafe-eval');
          console.log('✅ CSP不包含unsafe-eval');
        } else if (csp) {
          compliance.issues.push('CSP包含unsafe-eval');
          console.log('❌ CSP包含unsafe-eval');
        } else {
          compliance.issues.push('缺少CSP头部');
          console.log('❌ 缺少CSP头部');
        }
        
        // 检查3: HTTPS使用
        if (siteUrl.startsWith('https://')) {
          compliance.score += 20;
          compliance.passed.push('使用HTTPS');
          console.log('✅ 使用HTTPS');
        } else {
          compliance.issues.push('未使用HTTPS');
          console.log('❌ 未使用HTTPS');
        }
        
        // 检查4: 安全头部
        const securityHeaders = ['x-frame-options', 'x-xss-protection', 'x-content-type-options'];
        const presentHeaders = securityHeaders.filter(header => res.headers[header]);
        
        if (presentHeaders.length >= 2) {
          compliance.score += 15;
          compliance.passed.push(`安全头部完整 (${presentHeaders.length}/3)`);
          console.log(`✅ 安全头部: ${presentHeaders.join(', ')}`);
        } else {
          compliance.issues.push('安全头部不完整');
          console.log(`❌ 安全头部不完整: ${presentHeaders.length}/3`);
        }
        
        // 检查5: 隐私政策链接
        if (html.includes('privacy-policy') || html.includes('隐私政策')) {
          compliance.score += 10;
          compliance.passed.push('包含隐私政策链接');
          console.log('✅ 包含隐私政策链接');
        } else {
          compliance.issues.push('缺少隐私政策链接');
          console.log('❌ 缺少隐私政策链接');
        }
        
        // 检查6: 用户协议链接
        if (html.includes('terms-of-service') || html.includes('用户协议')) {
          compliance.score += 10;
          compliance.passed.push('包含用户协议链接');
          console.log('✅ 包含用户协议链接');
        } else {
          compliance.issues.push('缺少用户协议链接');
          console.log('❌ 缺少用户协议链接');
        }
        
        console.log(`\n📊 Google合规评分: ${compliance.score}/100`);
        resolve(compliance);
      });
    });
    
    req.on('error', (err) => {
      console.log(`❌ 合规检查失败: ${err.message}`);
      resolve({ score: 0, issues: ['检查失败'], passed: [] });
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      console.log('❌ 合规检查超时');
      resolve({ score: 0, issues: ['检查超时'], passed: [] });
    });
  });
}

// 2. 检查用户体验
function checkUserExperience(siteUrl) {
  return new Promise((resolve) => {
    console.log('\n👤 用户体验检查:');
    
    const protocol = siteUrl.startsWith('https') ? https : http;
    const startTime = Date.now();
    
    const req = protocol.get(siteUrl, (res) => {
      const loadTime = Date.now() - startTime;
      let html = '';
      res.on('data', chunk => html += chunk);
      res.on('end', () => {
        const ux = {
          score: 0,
          issues: [],
          passed: []
        };
        
        // 检查1: 页面加载速度
        if (loadTime < 2000) {
          ux.score += 25;
          ux.passed.push(`页面加载快速 (${loadTime}ms)`);
          console.log(`✅ 页面加载: ${loadTime}ms (快速)`);
        } else if (loadTime < 5000) {
          ux.score += 15;
          ux.passed.push(`页面加载一般 (${loadTime}ms)`);
          console.log(`⚠️ 页面加载: ${loadTime}ms (一般)`);
        } else {
          ux.issues.push(`页面加载过慢 (${loadTime}ms)`);
          console.log(`❌ 页面加载: ${loadTime}ms (过慢)`);
        }
        
        // 检查2: 响应状态
        if (res.statusCode === 200) {
          ux.score += 20;
          ux.passed.push('页面正常访问');
          console.log('✅ 页面状态: 200 OK');
        } else {
          ux.issues.push(`页面状态异常: ${res.statusCode}`);
          console.log(`❌ 页面状态: ${res.statusCode}`);
        }
        
        // 检查3: 移动端适配
        if (html.includes('viewport') && html.includes('responsive')) {
          ux.score += 15;
          ux.passed.push('移动端适配良好');
          console.log('✅ 移动端适配');
        } else if (html.includes('viewport')) {
          ux.score += 10;
          ux.passed.push('基本移动端适配');
          console.log('⚠️ 基本移动端适配');
        } else {
          ux.issues.push('缺少移动端适配');
          console.log('❌ 缺少移动端适配');
        }
        
        // 检查4: 多语言支持
        const languages = ['en', 'zh', 'fr', 'pt', 'ru'];
        const langSupport = languages.filter(lang => html.includes(`"${lang}"`)).length;
        
        if (langSupport >= 3) {
          ux.score += 15;
          ux.passed.push(`多语言支持 (${langSupport}种)`);
          console.log(`✅ 多语言支持: ${langSupport}种语言`);
        } else if (langSupport >= 2) {
          ux.score += 10;
          ux.passed.push(`基本多语言支持 (${langSupport}种)`);
          console.log(`⚠️ 多语言支持: ${langSupport}种语言`);
        } else {
          ux.issues.push('多语言支持不足');
          console.log('❌ 多语言支持不足');
        }
        
        // 检查5: 用户引导
        if (html.includes('免费') || html.includes('free')) {
          ux.score += 10;
          ux.passed.push('包含免费试用提示');
          console.log('✅ 包含免费试用提示');
        } else {
          ux.issues.push('缺少免费试用提示');
          console.log('❌ 缺少免费试用提示');
        }
        
        // 检查6: 错误处理
        if (html.includes('error') || html.includes('错误')) {
          ux.score += 5;
          ux.passed.push('包含错误处理');
          console.log('✅ 包含错误处理机制');
        }
        
        // 检查7: 加载状态
        if (html.includes('loading') || html.includes('加载')) {
          ux.score += 10;
          ux.passed.push('包含加载状态提示');
          console.log('✅ 包含加载状态提示');
        } else {
          ux.issues.push('缺少加载状态提示');
          console.log('❌ 缺少加载状态提示');
        }
        
        console.log(`\n📊 用户体验评分: ${ux.score}/100`);
        resolve(ux);
      });
    });
    
    req.on('error', (err) => {
      console.log(`❌ 用户体验检查失败: ${err.message}`);
      resolve({ score: 0, issues: ['检查失败'], passed: [] });
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      console.log('❌ 用户体验检查超时');
      resolve({ score: 0, issues: ['检查超时'], passed: [] });
    });
  });
}

// 3. 检查功能完整性
function checkFunctionality(siteUrl) {
  return new Promise((resolve) => {
    console.log('\n⚙️ 功能完整性检查:');
    
    const endpoints = [
      '/api/auth/providers',
      '/api/auth/session', 
      '/api/auth/csrf',
      '/sitemap.xml',
      '/robots.txt'
    ];
    
    const functionality = {
      score: 0,
      issues: [],
      passed: []
    };
    
    const checkEndpoint = (endpoint) => {
      return new Promise((resolveEndpoint) => {
        const url = `${siteUrl}${endpoint}`;
        const protocol = url.startsWith('https') ? https : http;
        
        const req = protocol.get(url, (res) => {
          if (res.statusCode === 200) {
            functionality.score += 20;
            functionality.passed.push(`${endpoint} 正常`);
            console.log(`✅ ${endpoint}: 200 OK`);
            resolveEndpoint(true);
          } else {
            functionality.issues.push(`${endpoint} 异常: ${res.statusCode}`);
            console.log(`❌ ${endpoint}: ${res.statusCode}`);
            resolveEndpoint(false);
          }
        });
        
        req.on('error', () => {
          functionality.issues.push(`${endpoint} 连接失败`);
          console.log(`❌ ${endpoint}: 连接失败`);
          resolveEndpoint(false);
        });
        
        req.setTimeout(5000, () => {
          req.destroy();
          functionality.issues.push(`${endpoint} 超时`);
          console.log(`❌ ${endpoint}: 超时`);
          resolveEndpoint(false);
        });
      });
    };
    
    Promise.all(endpoints.map(checkEndpoint)).then(() => {
      console.log(`\n📊 功能完整性评分: ${functionality.score}/100`);
      resolve(functionality);
    });
  });
}

// 4. 生成综合报告
function generateComprehensiveReport(compliance, ux, functionality) {
  console.log('\n📋 综合安全和用户体验报告:');
  console.log('=' * 60);
  
  const totalScore = Math.round((compliance.score + ux.score + functionality.score) / 3);
  
  console.log(`🛡️ Google安全合规: ${compliance.score}/100`);
  console.log(`👤 用户体验质量: ${ux.score}/100`);
  console.log(`⚙️ 功能完整性: ${functionality.score}/100`);
  console.log(`\n🎯 综合评分: ${totalScore}/100`);
  
  // 安全等级评估
  let securityLevel = '';
  let riskLevel = '';
  
  if (totalScore >= 90) {
    securityLevel = '🏆 优秀';
    riskLevel = '🟢 极低风险';
  } else if (totalScore >= 80) {
    securityLevel = '✅ 良好';
    riskLevel = '🟢 低风险';
  } else if (totalScore >= 70) {
    securityLevel = '⚠️ 一般';
    riskLevel = '🟡 中等风险';
  } else {
    securityLevel = '❌ 不足';
    riskLevel = '🔴 高风险';
  }
  
  console.log(`\n📊 安全等级: ${securityLevel}`);
  console.log(`🎯 Google风险: ${riskLevel}`);
  
  // Google判定风险评估
  console.log('\n🔍 Google危险网站判定风险:');
  
  const criticalIssues = [
    ...compliance.issues.filter(issue => 
      issue.includes('自定义登录') || 
      issue.includes('unsafe-eval') || 
      issue.includes('HTTPS')
    ),
    ...functionality.issues.filter(issue => 
      issue.includes('auth')
    )
  ];
  
  if (criticalIssues.length === 0 && compliance.score >= 70) {
    console.log('✅ 被Google标记为危险网站的风险: 极低');
    console.log('✅ 符合Google反欺骗政策');
    console.log('✅ 可以安全部署到生产环境');
  } else if (criticalIssues.length <= 2 && compliance.score >= 60) {
    console.log('⚠️ 被Google标记为危险网站的风险: 低');
    console.log('⚠️ 基本符合Google政策，建议优化');
  } else {
    console.log('❌ 被Google标记为危险网站的风险: 高');
    console.log('❌ 需要立即修复安全问题');
  }
  
  // 改进建议
  console.log('\n💡 改进建议:');
  
  const allIssues = [...compliance.issues, ...ux.issues, ...functionality.issues];
  if (allIssues.length === 0) {
    console.log('🎉 网站状态优秀，无需改进！');
  } else {
    console.log('🔧 需要改进的问题:');
    allIssues.slice(0, 5).forEach(issue => {
      console.log(`   - ${issue}`);
    });
  }
  
  // 用户体验建议
  if (ux.score < 80) {
    console.log('\n👤 用户体验优化建议:');
    console.log('1. 优化页面加载速度');
    console.log('2. 改善移动端适配');
    console.log('3. 添加更多用户引导');
    console.log('4. 完善错误处理机制');
  }
  
  return {
    totalScore,
    isGoogleSafe: criticalIssues.length === 0 && compliance.score >= 70,
    canDeploy: totalScore >= 70
  };
}

// 主函数
async function main() {
  try {
    const compliance = await checkGoogleCompliance(SITE_URL);
    const ux = await checkUserExperience(SITE_URL);
    const functionality = await checkFunctionality(SITE_URL);
    
    const result = generateComprehensiveReport(compliance, ux, functionality);
    
    console.log('\n🚀 最终结论:');
    if (result.isGoogleSafe) {
      console.log('✅ 网站安全，不会被Google标记为危险网站');
    } else {
      console.log('⚠️ 存在被Google标记的风险，需要修复');
    }
    
    if (result.canDeploy) {
      console.log('✅ 可以部署到生产环境');
    } else {
      console.log('❌ 建议修复问题后再部署');
    }
    
    process.exit(result.isGoogleSafe && result.canDeploy ? 0 : 1);
    
  } catch (error) {
    console.error('❌ 综合检查失败:', error.message);
    process.exit(1);
  }
}

main();
