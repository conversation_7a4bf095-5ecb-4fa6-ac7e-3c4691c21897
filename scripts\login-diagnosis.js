#!/usr/bin/env node

/**
 * 登录问题诊断脚本
 * 检查Google登录配置、CSP设置、NextAuth配置等
 */

require('dotenv').config({ path: '.env.local' });
require('dotenv').config({ path: '.env.development' });
require('dotenv').config({ path: '.env' });

const https = require('https');
const http = require('http');
const fs = require('fs');
const path = require('path');

const SITE_URL = process.env.NEXT_PUBLIC_WEB_URL || 'http://localhost:3000';

console.log('🔍 开始登录问题诊断...');
console.log(`检查网站: ${SITE_URL}`);

// 1. 检查环境变量配置
function checkEnvironmentVariables() {
  console.log('\n🔧 环境变量检查:');
  
  const requiredVars = [
    'AUTH_SECRET',
    'AUTH_GOOGLE_ID',
    'AUTH_GOOGLE_SECRET',
    'NEXT_PUBLIC_AUTH_GOOGLE_ENABLED'
  ];
  
  const issues = [];
  
  requiredVars.forEach(varName => {
    const value = process.env[varName];
    if (!value) {
      issues.push(`${varName} 未设置`);
      console.log(`❌ ${varName}: 未设置`);
    } else {
      console.log(`✅ ${varName}: 已设置`);
    }
  });
  
  // 检查Google One Tap是否被禁用
  const oneTapEnabled = process.env.NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED;
  if (oneTapEnabled === 'false') {
    console.log('✅ Google One Tap: 已禁用（符合安全要求）');
  } else {
    console.log('⚠️ Google One Tap: 未明确禁用');
  }
  
  return issues;
}

// 2. 检查NextAuth API端点
function checkNextAuthAPI(siteUrl) {
  return new Promise((resolve) => {
    console.log('\n🔐 NextAuth API检查:');
    
    const endpoints = [
      '/api/auth/providers',
      '/api/auth/session',
      '/api/auth/csrf'
    ];
    
    const checkEndpoint = (endpoint) => {
      return new Promise((resolveEndpoint) => {
        const url = `${siteUrl}${endpoint}`;
        const protocol = url.startsWith('https') ? https : http;
        
        const req = protocol.get(url, (res) => {
          console.log(`📍 ${endpoint}: ${res.statusCode} ${res.statusCode === 200 ? '✅' : '❌'}`);
          
          if (endpoint === '/api/auth/providers' && res.statusCode === 200) {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
              try {
                const providers = JSON.parse(data);
                if (providers.google) {
                  console.log('   ✅ Google提供商已配置');
                } else {
                  console.log('   ❌ Google提供商未找到');
                }
              } catch (e) {
                console.log('   ❌ 提供商数据解析失败');
              }
            });
          }
          
          resolveEndpoint(res.statusCode === 200);
        });
        
        req.on('error', (err) => {
          console.log(`📍 ${endpoint}: 连接失败 ❌`);
          resolveEndpoint(false);
        });
        
        req.setTimeout(5000, () => {
          req.destroy();
          console.log(`📍 ${endpoint}: 超时 ❌`);
          resolveEndpoint(false);
        });
      });
    };
    
    Promise.all(endpoints.map(checkEndpoint)).then(results => {
      const workingEndpoints = results.filter(Boolean).length;
      console.log(`\n📊 NextAuth API状态: ${workingEndpoints}/${endpoints.length}`);
      resolve(workingEndpoints === endpoints.length);
    });
  });
}

// 3. 检查CSP配置
function checkCSPConfiguration() {
  console.log('\n🛡️ CSP配置检查:');
  
  const configPath = path.join(__dirname, '../next.config.mjs');
  if (!fs.existsSync(configPath)) {
    console.log('❌ next.config.mjs 文件不存在');
    return false;
  }
  
  const configContent = fs.readFileSync(configPath, 'utf8');
  
  const requiredDomains = [
    'accounts.google.com',
    'oauth2.googleapis.com',
    'www.googleapis.com',
    'ssl.gstatic.com'
  ];
  
  let allDomainsFound = true;
  
  requiredDomains.forEach(domain => {
    if (configContent.includes(domain)) {
      console.log(`✅ CSP包含: ${domain}`);
    } else {
      console.log(`❌ CSP缺失: ${domain}`);
      allDomainsFound = false;
    }
  });
  
  // 检查script-src配置
  if (configContent.includes("'unsafe-inline'")) {
    console.log('✅ CSP允许内联脚本');
  } else {
    console.log('❌ CSP不允许内联脚本');
    allDomainsFound = false;
  }
  
  return allDomainsFound;
}

// 4. 检查登录页面
function checkSignInPage(siteUrl) {
  return new Promise((resolve) => {
    console.log('\n📄 登录页面检查:');
    
    const signInUrl = `${siteUrl}/auth/signin`;
    const protocol = signInUrl.startsWith('https') ? https : http;
    
    const req = protocol.get(signInUrl, (res) => {
      console.log(`📊 登录页面状态码: ${res.statusCode}`);
      
      if (res.statusCode === 200) {
        let html = '';
        res.on('data', chunk => html += chunk);
        res.on('end', () => {
          const issues = [];
          
          // 检查是否包含Google登录按钮
          if (html.includes('Google') || html.includes('google')) {
            console.log('✅ 页面包含Google登录元素');
          } else {
            issues.push('页面不包含Google登录元素');
            console.log('❌ 页面不包含Google登录元素');
          }
          
          // 检查是否有JavaScript错误
          if (html.includes('signIn')) {
            console.log('✅ 页面包含signIn函数调用');
          } else {
            issues.push('页面不包含signIn函数调用');
            console.log('❌ 页面不包含signIn函数调用');
          }
          
          // 检查是否有CSP错误
          if (html.includes('Content Security Policy')) {
            issues.push('页面可能有CSP错误');
            console.log('⚠️ 页面可能有CSP错误');
          }
          
          resolve(issues.length === 0);
        });
      } else {
        console.log('❌ 登录页面无法访问');
        resolve(false);
      }
    });
    
    req.on('error', (err) => {
      console.log(`❌ 登录页面检查失败: ${err.message}`);
      resolve(false);
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      console.log('❌ 登录页面检查超时');
      resolve(false);
    });
  });
}

// 5. 检查数据库连接
function checkDatabaseConnection() {
  console.log('\n🗄️ 数据库连接检查:');
  
  const dbUrl = process.env.DATABASE_URL;
  if (!dbUrl) {
    console.log('❌ DATABASE_URL 未设置');
    return false;
  }
  
  console.log('✅ DATABASE_URL 已设置');
  
  // 检查URL格式
  try {
    const url = new URL(dbUrl);
    console.log(`✅ 数据库类型: ${url.protocol.replace(':', '')}`);
    console.log(`✅ 数据库主机: ${url.hostname}`);
    return true;
  } catch (e) {
    console.log('❌ DATABASE_URL 格式无效');
    return false;
  }
}

// 6. 生成诊断报告
function generateDiagnosisReport(envIssues, authAPIWorking, cspValid, signInPageWorking, dbConnected) {
  console.log('\n📋 登录问题诊断报告:');
  console.log('=' * 50);
  
  let score = 100;
  const issues = [];
  
  // 环境变量评分
  if (envIssues.length > 0) {
    score -= envIssues.length * 20;
    issues.push(...envIssues);
  }
  
  // NextAuth API评分
  if (!authAPIWorking) {
    score -= 25;
    issues.push('NextAuth API端点异常');
  }
  
  // CSP配置评分
  if (!cspValid) {
    score -= 20;
    issues.push('CSP配置不完整');
  }
  
  // 登录页面评分
  if (!signInPageWorking) {
    score -= 20;
    issues.push('登录页面异常');
  }
  
  // 数据库连接评分
  if (!dbConnected) {
    score -= 15;
    issues.push('数据库连接配置异常');
  }
  
  score = Math.max(0, score);
  
  console.log(`\n📊 登录系统健康度: ${score}/100`);
  
  if (score >= 90) {
    console.log('✅ 登录系统运行良好');
  } else if (score >= 70) {
    console.log('⚠️ 登录系统基本正常，有小问题');
  } else if (score >= 50) {
    console.log('⚠️ 登录系统有问题，需要修复');
  } else {
    console.log('❌ 登录系统严重异常，需要立即修复');
  }
  
  if (issues.length > 0) {
    console.log('\n🔧 发现的问题:');
    issues.forEach(issue => console.log(`   - ${issue}`));
  }
  
  console.log('\n💡 修复建议:');
  if (envIssues.length > 0) {
    console.log('1. 检查并设置缺失的环境变量');
  }
  if (!authAPIWorking) {
    console.log('2. 检查NextAuth配置和API路由');
  }
  if (!cspValid) {
    console.log('3. 更新CSP配置，添加Google域名');
  }
  if (!signInPageWorking) {
    console.log('4. 检查登录页面组件和JavaScript');
  }
  if (!dbConnected) {
    console.log('5. 检查数据库连接配置');
  }
  
  return score;
}

// 主函数
async function main() {
  try {
    const envIssues = checkEnvironmentVariables();
    const authAPIWorking = await checkNextAuthAPI(SITE_URL);
    const cspValid = checkCSPConfiguration();
    const signInPageWorking = await checkSignInPage(SITE_URL);
    const dbConnected = checkDatabaseConnection();
    
    const score = generateDiagnosisReport(envIssues, authAPIWorking, cspValid, signInPageWorking, dbConnected);
    
    console.log('\n🚀 下一步行动:');
    if (score >= 80) {
      console.log('1. 登录系统基本正常，检查浏览器控制台错误');
      console.log('2. 清除浏览器缓存和Cookie');
      console.log('3. 尝试无痕模式登录');
    } else {
      console.log('1. 根据上述建议修复问题');
      console.log('2. 重新运行诊断验证修复效果');
      console.log('3. 检查浏览器开发者工具的错误信息');
    }
    
    process.exit(score < 50 ? 1 : 0);
    
  } catch (error) {
    console.error('❌ 登录诊断失败:', error.message);
    process.exit(1);
  }
}

main();
