#!/usr/bin/env node

/**
 * 全面的"欺骗性网站"检查脚本
 * 根据谷歌安全浏览标准检查可能被标记为欺骗性的内容
 */

const https = require('https');
const http = require('http');
const url = require('url');

const SITE_URL = process.env.NEXT_PUBLIC_WEB_URL || 'https://watermarkremover.top';

console.log('🔍 开始全面欺骗性网站检查...');
console.log(`检查网站: ${SITE_URL}`);

// 1. 检查可疑的重定向行为
function checkSuspiciousRedirects(siteUrl) {
  return new Promise((resolve) => {
    console.log('\n🔄 检查可疑重定向行为:');
    
    const req = https.get(siteUrl, (res) => {
      console.log(`✅ 主页状态码: ${res.statusCode}`);
      
      // 检查重定向链
      if (res.statusCode >= 300 && res.statusCode < 400) {
        const location = res.headers.location;
        console.log(`🔄 重定向到: ${location}`);
        
        // 检查是否重定向到可疑域名
        if (location && !location.includes(new URL(siteUrl).hostname)) {
          console.log('⚠️ 重定向到外部域名，可能被视为可疑');
        }
      } else {
        console.log('✅ 无重定向，直接返回内容');
      }
      
      resolve();
    });
    
    req.on('error', (err) => {
      console.log(`❌ 检查重定向失败: ${err.message}`);
      resolve();
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      console.log('❌ 重定向检查超时');
      resolve();
    });
  });
}

// 2. 检查页面内容是否存在欺骗性元素
function checkDeceptiveContent(siteUrl) {
  return new Promise((resolve) => {
    console.log('\n📄 检查页面内容欺骗性元素:');
    
    const req = https.get(siteUrl, (res) => {
      let html = '';
      res.on('data', chunk => html += chunk);
      res.on('end', () => {
        const issues = [];
        
        // 检查可疑的弹窗或alert
        if (html.includes('alert(') || html.includes('confirm(') || html.includes('prompt(')) {
          issues.push('发现JavaScript弹窗代码');
        }
        
        // 检查自动重定向脚本
        if (html.includes('window.location') || html.includes('location.href') || html.includes('location.replace')) {
          issues.push('发现JavaScript重定向代码');
        }
        
        // 检查隐藏的iframe
        if (html.includes('iframe') && (html.includes('display:none') || html.includes('visibility:hidden'))) {
          issues.push('发现隐藏的iframe');
        }
        
        // 检查可疑的外部链接
        const externalLinks = html.match(/href\s*=\s*["']https?:\/\/(?!watermarkremover\.top)[^"']+/gi);
        if (externalLinks && externalLinks.length > 0) {
          console.log('🔗 外部链接检查:');
          externalLinks.slice(0, 5).forEach(link => {
            console.log(`   - ${link}`);
          });
          if (externalLinks.length > 5) {
            console.log(`   ... 还有 ${externalLinks.length - 5} 个外部链接`);
          }
        }
        
        // 检查可疑的表单提交
        if (html.includes('<form') && html.includes('action=')) {
          const forms = html.match(/<form[^>]+action\s*=\s*["'][^"']+/gi);
          if (forms) {
            console.log('📝 表单提交检查:');
            forms.forEach(form => {
              console.log(`   - ${form}`);
            });
          }
        }
        
        if (issues.length > 0) {
          console.log('⚠️ 发现潜在欺骗性元素:');
          issues.forEach(issue => console.log(`   - ${issue}`));
        } else {
          console.log('✅ 未发现明显的欺骗性内容');
        }
        
        resolve();
      });
    });
    
    req.on('error', (err) => {
      console.log(`❌ 检查页面内容失败: ${err.message}`);
      resolve();
    });
    
    req.setTimeout(15000, () => {
      req.destroy();
      console.log('❌ 页面内容检查超时');
      resolve();
    });
  });
}

// 3. 检查OAuth流程的透明度
function checkOAuthTransparency(siteUrl) {
  return new Promise((resolve) => {
    console.log('\n🔐 检查OAuth流程透明度:');
    
    const req = https.get(siteUrl, (res) => {
      let html = '';
      res.on('data', chunk => html += chunk);
      res.on('end', () => {
        // 检查是否有明确的登录说明
        const hasLoginExplanation = html.includes('登录') && (
          html.includes('隐私') || 
          html.includes('保护') || 
          html.includes('积分') ||
          html.includes('免费')
        );
        
        // 检查是否有服务条款和隐私政策链接
        const hasTermsLink = html.includes('服务条款') || html.includes('Terms');
        const hasPrivacyLink = html.includes('隐私政策') || html.includes('Privacy');
        
        // 检查是否有明确的Google登录按钮文本
        const hasGoogleLoginText = html.includes('Google') && html.includes('登录');
        
        console.log(`${hasLoginExplanation ? '✅' : '❌'} 登录说明透明度`);
        console.log(`${hasTermsLink ? '✅' : '❌'} 服务条款链接`);
        console.log(`${hasPrivacyLink ? '✅' : '❌'} 隐私政策链接`);
        console.log(`${hasGoogleLoginText ? '✅' : '❌'} Google登录标识`);
        
        resolve();
      });
    });
    
    req.on('error', (err) => {
      console.log(`❌ 检查OAuth透明度失败: ${err.message}`);
      resolve();
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      console.log('❌ OAuth透明度检查超时');
      resolve();
    });
  });
}

// 4. 检查网站声誉和域名信息
function checkDomainReputation(siteUrl) {
  return new Promise((resolve) => {
    console.log('\n🌐 检查域名和网站信息:');
    
    const parsedUrl = url.parse(siteUrl);
    const domain = parsedUrl.hostname;
    
    console.log(`📋 域名: ${domain}`);
    console.log(`🔒 协议: ${parsedUrl.protocol}`);
    
    // 检查域名是否看起来可疑
    const suspiciousPatterns = [
      /\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/, // IP地址
      /[0-9]{5,}/, // 长数字
      /(.)\1{3,}/, // 重复字符
      /[^a-zA-Z0-9\-\.]/ // 特殊字符
    ];
    
    const isSuspiciousDomain = suspiciousPatterns.some(pattern => pattern.test(domain));
    console.log(`${isSuspiciousDomain ? '⚠️' : '✅'} 域名格式检查`);
    
    if (isSuspiciousDomain) {
      console.log('⚠️ 域名可能被视为可疑');
    }
    
    resolve();
  });
}

// 5. 检查用户体验欺骗性元素
function checkUXDeception(siteUrl) {
  return new Promise((resolve) => {
    console.log('\n👤 检查用户体验欺骗性元素:');
    
    const req = https.get(siteUrl, (res) => {
      let html = '';
      res.on('data', chunk => html += chunk);
      res.on('end', () => {
        const uxIssues = [];
        
        // 检查假的下载按钮
        if (html.includes('下载') && html.includes('onclick')) {
          uxIssues.push('可能存在误导性下载按钮');
        }
        
        // 检查假的安全警告
        if (html.includes('病毒') || html.includes('威胁') || html.includes('感染')) {
          uxIssues.push('可能存在虚假安全警告');
        }
        
        // 检查强制性弹窗
        if (html.includes('modal') && html.includes('backdrop') && html.includes('static')) {
          uxIssues.push('可能存在强制性弹窗');
        }
        
        // 检查隐藏的费用信息
        if ((html.includes('免费') || html.includes('free')) && html.includes('付费')) {
          uxIssues.push('可能存在隐藏的付费信息');
        }
        
        if (uxIssues.length > 0) {
          console.log('⚠️ 发现潜在UX欺骗元素:');
          uxIssues.forEach(issue => console.log(`   - ${issue}`));
        } else {
          console.log('✅ 未发现UX欺骗元素');
        }
        
        resolve();
      });
    });
    
    req.on('error', (err) => {
      console.log(`❌ 检查UX欺骗失败: ${err.message}`);
      resolve();
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      console.log('❌ UX欺骗检查超时');
      resolve();
    });
  });
}

// 主函数
async function main() {
  try {
    await checkSuspiciousRedirects(SITE_URL);
    await checkDeceptiveContent(SITE_URL);
    await checkOAuthTransparency(SITE_URL);
    await checkDomainReputation(SITE_URL);
    await checkUXDeception(SITE_URL);
    
    console.log('\n✅ 全面欺骗性网站检查完成!');
    console.log('\n📋 谷歌"欺骗性网站"常见原因:');
    console.log('1. 突然的重定向或弹窗');
    console.log('2. 隐藏的恶意代码或iframe');
    console.log('3. 误导性的下载按钮');
    console.log('4. 虚假的安全警告');
    console.log('5. 不透明的登录流程');
    console.log('6. 隐藏的费用或条款');
    console.log('7. 模仿知名网站的设计');
    
    console.log('\n💡 修复建议:');
    console.log('1. 确保所有用户交互都是透明的');
    console.log('2. 提供明确的服务说明');
    console.log('3. 避免突然的重定向或弹窗');
    console.log('4. 明确标识第三方服务（如Google登录）');
    console.log('5. 提供完整的隐私政策和服务条款');
    
  } catch (error) {
    console.error('❌ 欺骗性网站检查失败:', error.message);
    process.exit(1);
  }
}

main();
