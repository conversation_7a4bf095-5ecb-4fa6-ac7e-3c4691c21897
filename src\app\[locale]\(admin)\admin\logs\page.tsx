'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { 
  FileText, 
  AlertTriangle, 
  Info, 
  AlertCircle, 
  Bug, 
  Trash2, 
  Filter, 
  Download,
  Refresh<PERSON><PERSON>,
  Calendar,
  User,
  Activity
} from "lucide-react";
import { toast } from "sonner";
import moment from "moment";

interface SystemLog {
  id: number;
  level: 'info' | 'warn' | 'error' | 'debug';
  action: string;
  message: string;
  details?: string;
  user_uuid?: string;
  user_email?: string;
  ip_address?: string;
  user_agent?: string;
  module: string;
  resource_id?: string;
  created_at: string;
}

interface LogStats {
  total: number;
  byLevel: { [key: string]: number };
  byModule: { [key: string]: number };
  recentErrors: number;
}

export default function LogsPage() {
  const [logs, setLogs] = useState<SystemLog[]>([]);
  const [stats, setStats] = useState<LogStats>({
    total: 0,
    byLevel: {},
    byModule: {},
    recentErrors: 0,
  });
  const [loading, setLoading] = useState(true);
  const [selectedLog, setSelectedLog] = useState<SystemLog | null>(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filters, setFilters] = useState({
    level: '',
    module: '',
    startDate: '',
    endDate: '',
  });

  const levelColors = {
    info: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
    warn: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
    error: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
    debug: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
  };

  const levelIcons = {
    info: Info,
    warn: AlertTriangle,
    error: AlertCircle,
    debug: Bug,
  };

  const levelLabels = {
    info: '信息',
    warn: '警告',
    error: '错误',
    debug: '调试',
  };

  const fetchLogs = async (pageNum = 1) => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/logs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          page: pageNum,
          limit: 50,
          ...filters,
        }),
      });

      const result = await response.json();
      if (result.code === 0) {
        setLogs(result.data.logs);
        setTotalPages(result.data.totalPages);
      } else {
        toast.error(result.message || '获取日志列表失败');
      }
    } catch (error) {
      console.error('Failed to fetch logs:', error);
      toast.error('获取日志列表失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/admin/logs');
      const result = await response.json();
      if (result.code === 0) {
        setStats(result.data);
      }
    } catch (error) {
      console.error('Failed to fetch stats:', error);
    }
  };

  useEffect(() => {
    fetchStats();
    fetchLogs(page);
  }, [page]);

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const applyFilters = () => {
    setPage(1);
    fetchLogs(1);
  };

  const clearFilters = () => {
    setFilters({
      level: '',
      module: '',
      startDate: '',
      endDate: '',
    });
    setPage(1);
    fetchLogs(1);
  };

  const handleDeleteOldLogs = async (daysToKeep: number) => {
    try {
      const response = await fetch('/api/admin/logs', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ daysToKeep }),
      });

      const result = await response.json();
      if (result.code === 0) {
        toast.success(result.data.message);
        fetchStats();
        fetchLogs(page);
      } else {
        toast.error(result.message || '删除失败');
      }
    } catch (error) {
      console.error('Delete old logs failed:', error);
      toast.error('删除失败');
    }
  };

  const formatDetails = (details?: string) => {
    if (!details) return null;
    try {
      return JSON.stringify(JSON.parse(details), null, 2);
    } catch {
      return details;
    }
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">系统日志</h1>
          <p className="text-gray-600 dark:text-gray-400">
            查看和管理系统操作日志
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => fetchLogs(page)}>
            <RefreshCw className="w-4 h-4 mr-2" />
            刷新
          </Button>
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="destructive">
                <Trash2 className="w-4 h-4 mr-2" />
                清理日志
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>清理旧日志</AlertDialogTitle>
                <AlertDialogDescription>
                  删除30天前的日志记录。此操作不可撤销。
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>取消</AlertDialogCancel>
                <AlertDialogAction onClick={() => handleDeleteOldLogs(30)}>
                  确认删除
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <FileText className="w-4 h-4 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">总日志数</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <AlertCircle className="w-4 h-4 text-red-600" />
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">错误日志</p>
                <p className="text-2xl font-bold text-red-600">{stats.byLevel.error || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <AlertTriangle className="w-4 h-4 text-yellow-600" />
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">警告日志</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.byLevel.warn || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Activity className="w-4 h-4 text-orange-600" />
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">24h错误</p>
                <p className="text-2xl font-bold text-orange-600">{stats.recentErrors}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 过滤器 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="w-5 h-5" />
            过滤条件
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">日志级别</label>
              <Select value={filters.level} onValueChange={(value) => handleFilterChange('level', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="选择级别" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">全部</SelectItem>
                  <SelectItem value="info">信息</SelectItem>
                  <SelectItem value="warn">警告</SelectItem>
                  <SelectItem value="error">错误</SelectItem>
                  <SelectItem value="debug">调试</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">模块</label>
              <Select value={filters.module} onValueChange={(value) => handleFilterChange('module', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="选择模块" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">全部</SelectItem>
                  <SelectItem value="user">用户</SelectItem>
                  <SelectItem value="order">订单</SelectItem>
                  <SelectItem value="feedback">反馈</SelectItem>
                  <SelectItem value="contact">联系我们</SelectItem>
                  <SelectItem value="admin">管理员</SelectItem>
                  <SelectItem value="auth">认证</SelectItem>
                  <SelectItem value="payment">支付</SelectItem>
                  <SelectItem value="system">系统</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">开始日期</label>
              <Input
                type="date"
                value={filters.startDate}
                onChange={(e) => handleFilterChange('startDate', e.target.value)}
              />
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">结束日期</label>
              <Input
                type="date"
                value={filters.endDate}
                onChange={(e) => handleFilterChange('endDate', e.target.value)}
              />
            </div>
          </div>
          <div className="flex items-center gap-2 mt-4">
            <Button onClick={applyFilters}>
              <Filter className="w-4 h-4 mr-2" />
              应用过滤
            </Button>
            <Button variant="outline" onClick={clearFilters}>
              清除过滤
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 日志列表 */}
      <Card>
        <CardHeader>
          <CardTitle>日志列表</CardTitle>
          <CardDescription>系统操作和错误日志记录</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">加载中...</div>
          ) : logs.length === 0 ? (
            <div className="text-center py-8 text-gray-500">暂无日志数据</div>
          ) : (
            <div className="space-y-2">
              {logs.map((log) => {
                const LevelIcon = levelIcons[log.level];
                return (
                  <Card key={log.id} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <LevelIcon className="w-4 h-4" />
                            <Badge className={levelColors[log.level]}>
                              {levelLabels[log.level]}
                            </Badge>
                            <Badge variant="outline">{log.module}</Badge>
                            <span className="text-sm font-medium">{log.action}</span>
                          </div>
                          <p className="text-sm text-gray-700 dark:text-gray-300 mb-2">
                            {log.message}
                          </p>
                          <div className="flex items-center gap-4 text-xs text-gray-500">
                            <span className="flex items-center gap-1">
                              <Calendar className="w-3 h-3" />
                              {moment(log.created_at).format('YYYY-MM-DD HH:mm:ss')}
                            </span>
                            {log.user_email && (
                              <span className="flex items-center gap-1">
                                <User className="w-3 h-3" />
                                {log.user_email}
                              </span>
                            )}
                            {log.ip_address && (
                              <span>IP: {log.ip_address}</span>
                            )}
                          </div>
                        </div>
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setSelectedLog(log)}
                            >
                              查看详情
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-4xl">
                            <DialogHeader>
                              <DialogTitle>日志详情</DialogTitle>
                              <DialogDescription>
                                查看完整的日志信息和详细数据
                              </DialogDescription>
                            </DialogHeader>
                            {selectedLog && (
                              <div className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                  <div>
                                    <label className="text-sm font-medium">级别</label>
                                    <p className="text-sm text-gray-600">
                                      <Badge className={levelColors[selectedLog.level]}>
                                        {levelLabels[selectedLog.level]}
                                      </Badge>
                                    </p>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium">模块</label>
                                    <p className="text-sm text-gray-600">{selectedLog.module}</p>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium">操作</label>
                                    <p className="text-sm text-gray-600">{selectedLog.action}</p>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium">时间</label>
                                    <p className="text-sm text-gray-600">
                                      {moment(selectedLog.created_at).format('YYYY-MM-DD HH:mm:ss')}
                                    </p>
                                  </div>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">消息</label>
                                  <p className="text-sm text-gray-600 bg-gray-50 dark:bg-gray-800 p-3 rounded">
                                    {selectedLog.message}
                                  </p>
                                </div>
                                {selectedLog.details && (
                                  <div>
                                    <label className="text-sm font-medium">详细信息</label>
                                    <pre className="text-xs text-gray-600 bg-gray-50 dark:bg-gray-800 p-3 rounded overflow-auto max-h-60">
                                      {formatDetails(selectedLog.details)}
                                    </pre>
                                  </div>
                                )}
                                {selectedLog.user_email && (
                                  <div>
                                    <label className="text-sm font-medium">操作用户</label>
                                    <p className="text-sm text-gray-600">{selectedLog.user_email}</p>
                                  </div>
                                )}
                                {selectedLog.ip_address && (
                                  <div>
                                    <label className="text-sm font-medium">IP地址</label>
                                    <p className="text-sm text-gray-600">{selectedLog.ip_address}</p>
                                  </div>
                                )}
                                {selectedLog.resource_id && (
                                  <div>
                                    <label className="text-sm font-medium">资源ID</label>
                                    <p className="text-sm text-gray-600">{selectedLog.resource_id}</p>
                                  </div>
                                )}
                              </div>
                            )}
                          </DialogContent>
                        </Dialog>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}

          {/* 分页 */}
          {totalPages > 1 && (
            <div className="flex items-center justify-center gap-2 mt-6">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(page - 1)}
                disabled={page <= 1}
              >
                上一页
              </Button>
              <span className="text-sm text-gray-600">
                第 {page} 页，共 {totalPages} 页
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(page + 1)}
                disabled={page >= totalPages}
              >
                下一页
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
