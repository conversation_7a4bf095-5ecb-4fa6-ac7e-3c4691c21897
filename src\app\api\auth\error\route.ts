import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const error = searchParams.get('error');
  
  // 根据错误类型返回友好的错误信息
  let errorMessage = '登录失败，请稍后重试';
  let errorCode = 'UNKNOWN_ERROR';
  
  switch (error) {
    case 'Configuration':
      errorMessage = 'Google登录服务配置错误，请联系管理员';
      errorCode = 'CONFIG_ERROR';
      break;
    case 'AccessDenied':
      errorMessage = 'Google登录被拒绝，请确保允许应用访问您的基本信息';
      errorCode = 'ACCESS_DENIED';
      break;
    case 'Verification':
      errorMessage = 'Google账户验证失败，请重新尝试登录';
      errorCode = 'VERIFICATION_FAILED';
      break;
    case 'OAuthSignin':
      errorMessage = 'Google OAuth登录过程中发生错误，请重新尝试';
      errorCode = 'OAUTH_ERROR';
      break;
    case 'OAuthCallback':
      errorMessage = 'Google登录回调处理失败，请重新尝试';
      errorCode = 'CALLBACK_ERROR';
      break;
    case 'OAuthCreateAccount':
      errorMessage = 'Google账户创建失败，请稍后重试';
      errorCode = 'ACCOUNT_CREATE_ERROR';
      break;
    case 'EmailCreateAccount':
      errorMessage = '邮箱账户创建失败，请稍后重试';
      errorCode = 'EMAIL_CREATE_ERROR';
      break;
    case 'Callback':
      errorMessage = '登录回调处理失败，请重新尝试';
      errorCode = 'CALLBACK_FAILED';
      break;
    case 'OAuthAccountNotLinked':
      errorMessage = 'Google账户未关联，请使用相同的登录方式';
      errorCode = 'ACCOUNT_NOT_LINKED';
      break;
    case 'EmailSignin':
      errorMessage = '邮箱登录失败，请检查邮箱地址';
      errorCode = 'EMAIL_SIGNIN_ERROR';
      break;
    case 'CredentialsSignin':
      errorMessage = '登录凭据验证失败，请重新尝试';
      errorCode = 'CREDENTIALS_ERROR';
      break;
    case 'SessionRequired':
      errorMessage = '需要登录才能访问此页面';
      errorCode = 'SESSION_REQUIRED';
      break;
    case 'Default':
    default:
      errorMessage = 'Google登录过程中发生未知错误，请稍后重试';
      errorCode = 'LOGIN_FAILED';
      break;
  }
  
  // 重定向到主页并带上错误信息
  const redirectUrl = new URL('/', request.url);
  redirectUrl.searchParams.set('login_error', errorCode);
  redirectUrl.searchParams.set('error_message', encodeURIComponent(errorMessage));
  
  return NextResponse.redirect(redirectUrl);
}
