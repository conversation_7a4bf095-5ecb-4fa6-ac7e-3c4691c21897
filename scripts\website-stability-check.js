#!/usr/bin/env node

/**
 * 网站稳定性检查脚本
 * 检查API响应时间、数据库连接、组件性能等问题
 */

const https = require('https');
const http = require('http');

const SITE_URL = process.env.NEXT_PUBLIC_WEB_URL || 'https://watermarkremover.top';

console.log('🔍 开始网站稳定性检查...');
console.log(`检查网站: ${SITE_URL}`);

// 1. 检查主页加载时间
function checkPageLoadTime(siteUrl) {
  return new Promise((resolve) => {
    console.log('\n⏱️ 主页加载时间检查:');
    
    const startTime = Date.now();
    const protocol = siteUrl.startsWith('https') ? https : http;
    
    const req = protocol.get(siteUrl, (res) => {
      const loadTime = Date.now() - startTime;
      
      console.log(`📊 状态码: ${res.statusCode}`);
      console.log(`⏱️ 加载时间: ${loadTime}ms`);
      
      if (loadTime < 2000) {
        console.log('✅ 加载速度良好');
      } else if (loadTime < 5000) {
        console.log('⚠️ 加载速度一般');
      } else {
        console.log('❌ 加载速度过慢');
      }
      
      resolve(loadTime);
    });
    
    req.on('error', (err) => {
      console.log(`❌ 主页加载失败: ${err.message}`);
      resolve(-1);
    });
    
    req.setTimeout(15000, () => {
      req.destroy();
      console.log('❌ 主页加载超时');
      resolve(-1);
    });
  });
}

// 2. 检查API响应时间
function checkAPIResponseTime(baseUrl) {
  return new Promise((resolve) => {
    console.log('\n🔌 API响应时间检查:');
    
    const apiEndpoints = [
      '/api/auth/session',
      '/api/auth/providers',
      '/api/security-check'
    ];
    
    const checkEndpoint = (endpoint) => {
      return new Promise((resolveEndpoint) => {
        const startTime = Date.now();
        const url = `${baseUrl}${endpoint}`;
        const protocol = url.startsWith('https') ? https : http;
        
        const req = protocol.get(url, (res) => {
          const responseTime = Date.now() - startTime;
          
          console.log(`📍 ${endpoint}: ${res.statusCode} - ${responseTime}ms`);
          
          if (responseTime < 1000) {
            console.log(`   ✅ 响应速度良好`);
          } else if (responseTime < 3000) {
            console.log(`   ⚠️ 响应速度一般`);
          } else {
            console.log(`   ❌ 响应速度过慢`);
          }
          
          resolveEndpoint(responseTime);
        });
        
        req.on('error', (err) => {
          console.log(`   ❌ ${endpoint}: 请求失败 - ${err.message}`);
          resolveEndpoint(-1);
        });
        
        req.setTimeout(10000, () => {
          req.destroy();
          console.log(`   ❌ ${endpoint}: 请求超时`);
          resolveEndpoint(-1);
        });
      });
    };
    
    Promise.all(apiEndpoints.map(checkEndpoint)).then(results => {
      const avgTime = results.filter(t => t > 0).reduce((a, b) => a + b, 0) / results.filter(t => t > 0).length;
      console.log(`\n📊 API平均响应时间: ${avgTime.toFixed(0)}ms`);
      resolve(results);
    });
  });
}

// 3. 检查资源加载
function checkResourceLoading(siteUrl) {
  return new Promise((resolve) => {
    console.log('\n📦 资源加载检查:');
    
    const protocol = siteUrl.startsWith('https') ? https : http;
    
    const req = protocol.get(siteUrl, (res) => {
      let html = '';
      res.on('data', chunk => html += chunk);
      res.on('end', () => {
        const issues = [];
        
        // 检查是否有大量的外部资源
        const externalResources = html.match(/src\s*=\s*["']https?:\/\/[^"']+/gi) || [];
        console.log(`🔗 外部资源数量: ${externalResources.length}`);
        
        if (externalResources.length > 20) {
          issues.push('外部资源过多，可能影响加载速度');
        }
        
        // 检查是否有内联样式过多
        const inlineStyles = html.match(/style\s*=\s*["'][^"']+/gi) || [];
        console.log(`🎨 内联样式数量: ${inlineStyles.length}`);
        
        if (inlineStyles.length > 50) {
          issues.push('内联样式过多，建议优化CSS');
        }
        
        // 检查是否有大量的脚本标签
        const scriptTags = html.match(/<script[^>]*>/gi) || [];
        console.log(`📜 脚本标签数量: ${scriptTags.length}`);
        
        if (scriptTags.length > 15) {
          issues.push('脚本标签过多，可能影响性能');
        }
        
        if (issues.length === 0) {
          console.log('✅ 资源加载配置良好');
        } else {
          console.log('⚠️ 发现资源加载问题:');
          issues.forEach(issue => console.log(`   - ${issue}`));
        }
        
        resolve(issues);
      });
    });
    
    req.on('error', (err) => {
      console.log(`❌ 资源检查失败: ${err.message}`);
      resolve(['资源检查失败']);
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      console.log('❌ 资源检查超时');
      resolve(['资源检查超时']);
    });
  });
}

// 4. 检查错误率
function checkErrorRate(baseUrl) {
  return new Promise((resolve) => {
    console.log('\n❌ 错误率检查:');
    
    const testUrls = [
      '/',
      '/zh',
      '/en',
      '/api/auth/session',
      '/api/auth/providers'
    ];
    
    const checkUrl = (path) => {
      return new Promise((resolveUrl) => {
        const url = `${baseUrl}${path}`;
        const protocol = url.startsWith('https') ? https : http;
        
        const req = protocol.get(url, (res) => {
          const isError = res.statusCode >= 400;
          console.log(`📍 ${path}: ${res.statusCode} ${isError ? '❌' : '✅'}`);
          resolveUrl(isError);
        });
        
        req.on('error', (err) => {
          console.log(`📍 ${path}: 连接失败 ❌`);
          resolveUrl(true);
        });
        
        req.setTimeout(5000, () => {
          req.destroy();
          console.log(`📍 ${path}: 超时 ❌`);
          resolveUrl(true);
        });
      });
    };
    
    Promise.all(testUrls.map(checkUrl)).then(results => {
      const errorCount = results.filter(Boolean).length;
      const errorRate = (errorCount / results.length) * 100;
      
      console.log(`\n📊 错误率: ${errorRate.toFixed(1)}% (${errorCount}/${results.length})`);
      
      if (errorRate === 0) {
        console.log('✅ 无错误，网站运行正常');
      } else if (errorRate < 20) {
        console.log('⚠️ 少量错误，需要关注');
      } else {
        console.log('❌ 错误率过高，需要立即修复');
      }
      
      resolve(errorRate);
    });
  });
}

// 5. 生成稳定性报告
function generateStabilityReport(loadTime, apiTimes, resourceIssues, errorRate) {
  console.log('\n📋 网站稳定性报告:');
  console.log('=' * 50);
  
  let score = 100;
  const issues = [];
  
  // 评估加载时间
  if (loadTime > 5000) {
    score -= 30;
    issues.push('主页加载时间过长');
  } else if (loadTime > 2000) {
    score -= 15;
    issues.push('主页加载时间偏长');
  }
  
  // 评估API响应时间
  const avgApiTime = apiTimes.filter(t => t > 0).reduce((a, b) => a + b, 0) / apiTimes.filter(t => t > 0).length;
  if (avgApiTime > 3000) {
    score -= 25;
    issues.push('API响应时间过长');
  } else if (avgApiTime > 1000) {
    score -= 10;
    issues.push('API响应时间偏长');
  }
  
  // 评估资源问题
  if (resourceIssues.length > 0) {
    score -= resourceIssues.length * 5;
    issues.push(...resourceIssues);
  }
  
  // 评估错误率
  if (errorRate > 20) {
    score -= 30;
    issues.push('错误率过高');
  } else if (errorRate > 0) {
    score -= errorRate;
    issues.push('存在错误');
  }
  
  score = Math.max(0, score);
  
  console.log(`\n📊 稳定性评分: ${score}/100`);
  
  if (score >= 90) {
    console.log('✅ 网站稳定性优秀');
  } else if (score >= 70) {
    console.log('⚠️ 网站稳定性良好，有改进空间');
  } else if (score >= 50) {
    console.log('⚠️ 网站稳定性一般，需要优化');
  } else {
    console.log('❌ 网站稳定性差，需要立即修复');
  }
  
  if (issues.length > 0) {
    console.log('\n🔧 发现的问题:');
    issues.forEach(issue => console.log(`   - ${issue}`));
  }
  
  console.log('\n💡 优化建议:');
  if (loadTime > 2000) {
    console.log('1. 优化图片和资源加载');
    console.log('2. 启用CDN加速');
    console.log('3. 压缩CSS和JavaScript');
  }
  
  if (avgApiTime > 1000) {
    console.log('4. 优化数据库查询');
    console.log('5. 添加API缓存');
    console.log('6. 检查数据库连接配置');
  }
  
  if (errorRate > 0) {
    console.log('7. 修复API错误');
    console.log('8. 检查路由配置');
    console.log('9. 添加错误处理');
  }
  
  return score;
}

// 主函数
async function main() {
  try {
    const loadTime = await checkPageLoadTime(SITE_URL);
    const apiTimes = await checkAPIResponseTime(SITE_URL);
    const resourceIssues = await checkResourceLoading(SITE_URL);
    const errorRate = await checkErrorRate(SITE_URL);
    
    const score = generateStabilityReport(loadTime, apiTimes, resourceIssues, errorRate);
    
    console.log('\n🚀 下一步行动:');
    if (score >= 80) {
      console.log('1. 网站运行良好，继续监控');
      console.log('2. 定期运行此检查脚本');
    } else {
      console.log('1. 根据上述建议进行优化');
      console.log('2. 重新运行检查验证改进效果');
      console.log('3. 考虑升级服务器配置');
    }
    
    process.exit(score < 50 ? 1 : 0);
    
  } catch (error) {
    console.error('❌ 稳定性检查失败:', error.message);
    process.exit(1);
  }
}

main();
