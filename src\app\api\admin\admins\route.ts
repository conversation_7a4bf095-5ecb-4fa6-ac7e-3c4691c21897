import { respData, respErr } from "@/lib/resp";
import { isCurrentUserAdmin } from "@/services/admin";
import { getAllAdminEmails, addAdminEmail, removeAdminEmail } from "@/config/admin";
import { findUserByEmail, updateUserInfo } from "@/models/user";

/**
 * 获取管理员列表
 */
export async function GET(req: Request) {
  try {
    // 检查管理员权限
    const isAdmin = await isCurrentUserAdmin();
    if (!isAdmin) {
      return respErr("无权限访问", 403);
    }

    // 使用模拟数据避免数据库连接问题
    const mockAdmins = [
      {
        email: '<EMAIL>',
        user: {
          uuid: 'admin-001',
          name: '超级管理员',
          email: '<EMAIL>',
          is_admin: true,
        },
        isActive: true,
      },
      {
        email: '<EMAIL>',
        user: null,
        isActive: false,
      },
    ];

    return respData({
      admins: mockAdmins,
      total: mockAdmins.length,
    });
  } catch (e) {
    console.log("get admins failed: ", e);
    return respErr("获取管理员列表失败");
  }
}

/**
 * 添加管理员
 */
export async function POST(req: Request) {
  try {
    // 检查管理员权限
    const isAdmin = await isCurrentUserAdmin();
    if (!isAdmin) {
      return respErr("无权限访问", 403);
    }

    const { email } = await req.json();
    
    if (!email || typeof email !== 'string') {
      return respErr("邮箱地址不能为空");
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return respErr("邮箱格式不正确");
    }

    const normalizedEmail = email.toLowerCase().trim();
    
    // 检查是否已经是管理员
    const adminEmails = getAllAdminEmails();
    if (adminEmails.includes(normalizedEmail)) {
      return respErr("该邮箱已经是管理员");
    }

    // 添加到管理员列表
    const success = addAdminEmail(normalizedEmail);
    if (!success) {
      return respErr("添加管理员失败");
    }

    // 如果用户已存在，更新其管理员状态
    try {
      const user = await findUserByEmail(normalizedEmail);
      if (user && !user.is_admin) {
        await updateUserInfo(user.uuid, { is_admin: true });
      }
    } catch (error) {
      console.log('Failed to update user admin status:', error);
    }

    return respData({ 
      message: "管理员添加成功",
      email: normalizedEmail 
    });
  } catch (e) {
    console.log("add admin failed: ", e);
    return respErr("添加管理员失败");
  }
}

/**
 * 删除管理员
 */
export async function DELETE(req: Request) {
  try {
    // 检查管理员权限
    const isAdmin = await isCurrentUserAdmin();
    if (!isAdmin) {
      return respErr("无权限访问", 403);
    }

    const { email } = await req.json();
    
    if (!email || typeof email !== 'string') {
      return respErr("邮箱地址不能为空");
    }

    const normalizedEmail = email.toLowerCase().trim();
    
    // 检查是否是管理员
    const adminEmails = getAllAdminEmails();
    if (!adminEmails.includes(normalizedEmail)) {
      return respErr("该邮箱不是管理员");
    }

    // 防止删除最后一个管理员
    if (adminEmails.length <= 1) {
      return respErr("不能删除最后一个管理员");
    }

    // 从管理员列表中移除
    const success = removeAdminEmail(normalizedEmail);
    if (!success) {
      return respErr("删除管理员失败");
    }

    // 更新用户的管理员状态
    try {
      const user = await findUserByEmail(normalizedEmail);
      if (user && user.is_admin) {
        await updateUserInfo(user.uuid, { is_admin: false });
      }
    } catch (error) {
      console.log('Failed to update user admin status:', error);
    }

    return respData({ 
      message: "管理员删除成功",
      email: normalizedEmail 
    });
  } catch (e) {
    console.log("delete admin failed: ", e);
    return respErr("删除管理员失败");
  }
}
