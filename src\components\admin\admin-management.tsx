'use client'

import { useState, useEffect, memo } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Plus, Trash2, Mail, User, Shield, AlertTriangle } from 'lucide-react'
import { toast } from 'sonner'

interface AdminUser {
  email: string
  user: {
    uuid: string
    name: string
    email: string
    is_admin: boolean
  } | null
  isActive: boolean
}

interface AdminsResponse {
  admins: AdminUser[]
  total: number
}

// 简单的缓存机制
let adminCache: { data: AdminUser[]; timestamp: number } | null = null
const CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

const AdminManagement = memo(function AdminManagement() {
  const [admins, setAdmins] = useState<AdminUser[]>([])
  const [loading, setLoading] = useState(true)
  const [newAdminEmail, setNewAdminEmail] = useState('')
  const [adding, setAdding] = useState(false)

  // 获取管理员列表
  const fetchAdmins = async () => {
    try {
      // 检查缓存
      if (adminCache && Date.now() - adminCache.timestamp < CACHE_DURATION) {
        setAdmins(adminCache.data)
        setLoading(false)
        return
      }

      const response = await fetch('/api/admin/admins')
      const data = await response.json()

      if (data.code === 0) {
        setAdmins(data.data.admins)
        // 更新缓存
        adminCache = {
          data: data.data.admins,
          timestamp: Date.now()
        }
      } else {
        toast.error(data.message || '获取管理员列表失败')
      }
    } catch (error) {
      console.error('Failed to fetch admins:', error)
      toast.error('获取管理员列表失败')
    } finally {
      setLoading(false)
    }
  }

  // 添加管理员
  const handleAddAdmin = async () => {
    if (!newAdminEmail.trim()) {
      toast.error('请输入邮箱地址')
      return
    }

    setAdding(true)
    try {
      const response = await fetch('/api/admin/admins', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: newAdminEmail.trim() }),
      })
      
      const data = await response.json()
      
      if (data.code === 0) {
        toast.success('管理员添加成功')
        setNewAdminEmail('')
        fetchAdmins()
      } else {
        toast.error(data.message || '添加管理员失败')
      }
    } catch (error) {
      console.error('Failed to add admin:', error)
      toast.error('添加管理员失败')
    } finally {
      setAdding(false)
    }
  }

  // 删除管理员
  const handleDeleteAdmin = async (email: string) => {
    if (!confirm(`确定要删除管理员 ${email} 吗？`)) {
      return
    }

    try {
      const response = await fetch('/api/admin/admins', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      })
      
      const data = await response.json()
      
      if (data.success) {
        toast.success('管理员删除成功')
        fetchAdmins()
      } else {
        toast.error(data.message || '删除管理员失败')
      }
    } catch (error) {
      console.error('Failed to delete admin:', error)
      toast.error('删除管理员失败')
    }
  }

  useEffect(() => {
    fetchAdmins()
  }, [])

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>管理员管理</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="w-5 h-5" />
          管理员管理
        </CardTitle>
        <CardDescription>
          管理系统管理员账户，添加或删除管理员权限
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 添加管理员 */}
        <div className="flex gap-2">
          <Input
            placeholder="输入邮箱地址"
            value={newAdminEmail}
            onChange={(e) => setNewAdminEmail(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleAddAdmin()}
            className="flex-1"
          />
          <Button 
            onClick={handleAddAdmin} 
            disabled={adding}
            className="flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            {adding ? '添加中...' : '添加管理员'}
          </Button>
        </div>

        {/* 管理员列表 */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
            当前管理员 ({admins.length})
          </h3>
          
          {admins.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              暂无管理员
            </div>
          ) : (
            <div className="space-y-2">
              {admins.map((admin) => (
                <div
                  key={admin.email}
                  className="flex items-center justify-between p-3 border rounded-lg bg-gray-50 dark:bg-gray-800"
                >
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      {admin.isActive ? (
                        <User className="w-4 h-4 text-green-500" />
                      ) : (
                        <Mail className="w-4 h-4 text-gray-400" />
                      )}
                      <span className="font-medium">{admin.email}</span>
                    </div>
                    
                    <div className="flex gap-1">
                      {admin.isActive ? (
                        <Badge variant="default" className="text-xs">
                          已激活
                        </Badge>
                      ) : (
                        <Badge variant="secondary" className="text-xs">
                          待激活
                        </Badge>
                      )}
                      
                      {admin.user?.name && (
                        <Badge variant="outline" className="text-xs">
                          {admin.user.name}
                        </Badge>
                      )}
                    </div>
                  </div>

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteAdmin(admin.email)}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 说明 */}
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <div className="flex items-start gap-2">
            <AlertTriangle className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-blue-800 dark:text-blue-200">
              <p className="font-medium mb-1">说明：</p>
              <ul className="space-y-1 text-xs">
                <li>• 添加邮箱后，该用户下次登录时将自动获得管理员权限</li>
                <li>• "已激活"表示该邮箱对应的用户已经登录过系统</li>
                <li>• "待激活"表示该邮箱还未在系统中注册或登录</li>
                <li>• 配置文件中的管理员无法通过此界面删除</li>
              </ul>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
})

export default AdminManagement
