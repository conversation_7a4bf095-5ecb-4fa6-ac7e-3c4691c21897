import { NextResponse } from 'next/server';
import { SECURITY_CONFIG, generateCSP, generateCSPWithNonce, generateNonce } from '@/config/security';

export function addSecurityHeaders(response: NextResponse) {
  // 生成nonce
  const nonce = generateNonce();
  response.headers.set('x-nonce', nonce);

  // 添加所有安全头
  Object.entries(SECURITY_CONFIG.SECURITY_HEADERS).forEach(([key, value]) => {
    response.headers.set(key, value);
  });

  // 暂时禁用CSP进行调试
  // response.headers.set('Content-Security-Policy', generateCSPWithNonce(nonce));

  return response;
}

export function isSecureRequest(request: Request): boolean {
  const url = new URL(request.url);
  return url.protocol === 'https:' || url.hostname === 'localhost';
}

export function isDomainAllowed(domain: string): boolean {
  return SECURITY_CONFIG.ALLOWED_DOMAINS.some(allowed => 
    domain === allowed || domain.endsWith(`.${allowed}`)
  );
}