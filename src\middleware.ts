import createMiddleware from "next-intl/middleware";
import { routing } from "./i18n/routing";
import { NextRequest, NextResponse } from "next/server";
import { addSecurityHeaders, isSecureRequest } from "./lib/security";

const intlMiddleware = createMiddleware({
  ...routing,
  // 启用cookie检测，确保用户的语言偏好被保持
  localeDetection: true,
});

export default function middleware(request: NextRequest) {
  // 安全检查：在生产环境中确保使用HTTPS
  if (process.env.NODE_ENV === 'production' && !isSecureRequest(request)) {
    const url = request.nextUrl.clone();
    url.protocol = 'https:';
    return NextResponse.redirect(url);
  }

  // 使用 next-intl 的中间件处理所有路由
  const response = intlMiddleware(request);
  
  // 添加安全头
  return addSecurityHeaders(response);
}

export const config = {
  matcher: [
    "/",
    "/(en|zh|fr|ru|pt)/:path*",
    "/((?!api/|_next|_vercel|.*\\..*).*)",
  ],
};
