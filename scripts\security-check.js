#!/usr/bin/env node

/**
 * 安全检查脚本
 * 检查网站的安全配置是否正确
 */

const https = require('https');
const http = require('http');

const SITE_URL = process.env.NEXT_PUBLIC_WEB_URL || 'https://watermarkremover.top';

console.log('🔍 开始安全检查...');
console.log(`检查网站: ${SITE_URL}`);

// 检查CSP头
function checkCSPHeaders(url) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https') ? https : http;
    
    const req = protocol.get(url, (res) => {
      const csp = res.headers['content-security-policy'];
      
      console.log('\n📋 CSP 检查结果:');
      if (csp) {
        console.log('✅ CSP 头存在');
        
        // 检查是否包含必要的域名
        const requiredDomains = [
          'api.textin.com',
          'api.klingai.com',
          'www.google-analytics.com',
          'api.paypal.com'
        ];
        
        requiredDomains.forEach(domain => {
          if (csp.includes(domain)) {
            console.log(`✅ ${domain} 已在 CSP 中允许`);
          } else {
            console.log(`❌ ${domain} 未在 CSP 中允许`);
          }
        });
        
        console.log('\n📝 完整 CSP 策略:');
        console.log(csp);
      } else {
        console.log('❌ 未找到 CSP 头');
      }
      
      // 检查其他安全头
      console.log('\n🛡️ 其他安全头检查:');
      const securityHeaders = {
        'x-frame-options': 'X-Frame-Options',
        'x-content-type-options': 'X-Content-Type-Options',
        'referrer-policy': 'Referrer-Policy',
        'strict-transport-security': 'Strict-Transport-Security'
      };
      
      Object.entries(securityHeaders).forEach(([header, name]) => {
        if (res.headers[header]) {
          console.log(`✅ ${name}: ${res.headers[header]}`);
        } else {
          console.log(`❌ ${name}: 未设置`);
        }
      });
      
      resolve();
    });
    
    req.on('error', (err) => {
      console.error('❌ 请求失败:', err.message);
      reject(err);
    });
    
    req.setTimeout(10000, () => {
      console.error('❌ 请求超时');
      req.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

// 检查robots.txt
function checkRobotsTxt(baseUrl) {
  return new Promise((resolve, reject) => {
    const robotsUrl = `${baseUrl}/robots.txt`;
    const protocol = robotsUrl.startsWith('https') ? https : http;
    
    const req = protocol.get(robotsUrl, (res) => {
      console.log('\n🤖 Robots.txt 检查:');
      if (res.statusCode === 200) {
        console.log('✅ robots.txt 存在');
        
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          if (data.includes('Sitemap:')) {
            console.log('✅ Sitemap 已在 robots.txt 中声明');
          } else {
            console.log('⚠️ Sitemap 未在 robots.txt 中声明');
          }
          
          if (data.includes('Disallow: /api/')) {
            console.log('✅ API 路径已被禁止爬取');
          } else {
            console.log('⚠️ API 路径未被禁止爬取');
          }
          
          resolve();
        });
      } else {
        console.log(`❌ robots.txt 不存在 (状态码: ${res.statusCode})`);
        resolve();
      }
    });
    
    req.on('error', (err) => {
      console.log(`❌ 无法访问 robots.txt: ${err.message}`);
      resolve();
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      console.log('❌ robots.txt 请求超时');
      resolve();
    });
  });
}

// 主函数
async function main() {
  try {
    await checkCSPHeaders(SITE_URL);
    await checkRobotsTxt(SITE_URL);
    
    console.log('\n✅ 安全检查完成!');
    console.log('\n💡 建议:');
    console.log('1. 确保所有外部API域名都在CSP中正确配置');
    console.log('2. 定期检查安全头配置');
    console.log('3. 监控谷歌安全浏览状态');
    console.log('4. 保持依赖包更新');
    
  } catch (error) {
    console.error('❌ 安全检查失败:', error.message);
    process.exit(1);
  }
}

main();
