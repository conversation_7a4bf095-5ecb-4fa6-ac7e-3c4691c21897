'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Mail, MessageCircle, Clock, CheckCircle, X, Eye, Reply, Archive } from "lucide-react";
import { toast } from "sonner";
import moment from "moment";

interface Contact {
  id: number;
  name: string;
  email: string;
  subject: string;
  message: string;
  status: 'unread' | 'read' | 'replied' | 'closed';
  admin_reply?: string;
  replied_at?: string;
  replied_by?: string;
  created_at: string;
  updated_at: string;
  user_uuid?: string;
  ip_address?: string;
}

interface ContactStats {
  total: number;
  unread: number;
  read: number;
  replied: number;
  closed: number;
}

export default function ContactsPage() {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [stats, setStats] = useState<ContactStats>({
    total: 0,
    unread: 0,
    read: 0,
    replied: 0,
    closed: 0,
  });
  const [loading, setLoading] = useState(true);
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [replyText, setReplyText] = useState('');
  const [replying, setReplying] = useState(false);
  const [currentTab, setCurrentTab] = useState('all');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const statusColors = {
    unread: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
    read: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
    replied: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    closed: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
  };

  const statusLabels = {
    unread: '未读',
    read: '已读',
    replied: '已回复',
    closed: '已关闭',
  };

  const fetchContacts = async (status?: string, pageNum = 1) => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/contacts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          page: pageNum,
          limit: 20,
          status: status === 'all' ? undefined : status,
        }),
      });

      const result = await response.json();
      if (result.code === 0) {
        setContacts(result.data.contacts);
        setTotalPages(result.data.totalPages);
      } else {
        toast.error(result.message || '获取联系我们列表失败');
      }
    } catch (error) {
      console.error('Failed to fetch contacts:', error);
      toast.error('获取联系我们列表失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/admin/contacts');
      const result = await response.json();
      if (result.code === 0) {
        setStats(result.data);
      }
    } catch (error) {
      console.error('Failed to fetch stats:', error);
    }
  };

  useEffect(() => {
    fetchStats();
    fetchContacts(currentTab, page);
  }, [currentTab, page]);

  const handleContactAction = async (contactId: number, action: string, data?: any) => {
    try {
      const response = await fetch(`/api/admin/contacts/${contactId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action, ...data }),
      });

      const result = await response.json();
      if (result.code === 0) {
        toast.success('操作成功');
        fetchContacts(currentTab, page);
        fetchStats();
        setSelectedContact(null);
        setReplyText('');
      } else {
        toast.error(result.message || '操作失败');
      }
    } catch (error) {
      console.error('Contact action failed:', error);
      toast.error('操作失败');
    }
  };

  const handleReply = async () => {
    if (!selectedContact || !replyText.trim()) {
      toast.error('请输入回复内容');
      return;
    }

    setReplying(true);
    try {
      await handleContactAction(selectedContact.id, 'reply', { reply: replyText });
    } finally {
      setReplying(false);
    }
  };

  const handleMarkAsRead = async (contact: Contact) => {
    if (contact.status === 'unread') {
      await handleContactAction(contact.id, 'mark_read');
    }
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">联系我们管理</h1>
          <p className="text-gray-600 dark:text-gray-400">
            管理用户提交的联系表单，查看和回复用户消息
          </p>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Mail className="w-4 h-4 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">总数</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <MessageCircle className="w-4 h-4 text-red-600" />
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">未读</p>
                <p className="text-2xl font-bold text-red-600">{stats.unread}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Eye className="w-4 h-4 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">已读</p>
                <p className="text-2xl font-bold text-blue-600">{stats.read}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="w-4 h-4 text-green-600" />
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">已回复</p>
                <p className="text-2xl font-bold text-green-600">{stats.replied}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Archive className="w-4 h-4 text-gray-600" />
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">已关闭</p>
                <p className="text-2xl font-bold text-gray-600">{stats.closed}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 联系我们列表 */}
      <Card>
        <CardHeader>
          <CardTitle>联系我们列表</CardTitle>
          <CardDescription>查看和管理用户提交的联系表单</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={currentTab} onValueChange={setCurrentTab}>
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="all">全部</TabsTrigger>
              <TabsTrigger value="unread">未读</TabsTrigger>
              <TabsTrigger value="read">已读</TabsTrigger>
              <TabsTrigger value="replied">已回复</TabsTrigger>
              <TabsTrigger value="closed">已关闭</TabsTrigger>
            </TabsList>

            <TabsContent value={currentTab} className="mt-6">
              {loading ? (
                <div className="text-center py-8">加载中...</div>
              ) : contacts.length === 0 ? (
                <div className="text-center py-8 text-gray-500">暂无数据</div>
              ) : (
                <div className="space-y-4">
                  {contacts.map((contact) => (
                    <Card key={contact.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <h3 className="font-semibold">{contact.subject}</h3>
                              <Badge className={statusColors[contact.status]}>
                                {statusLabels[contact.status]}
                              </Badge>
                            </div>
                            <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                              <span className="font-medium">{contact.name}</span> ({contact.email})
                            </div>
                            <p className="text-sm text-gray-700 dark:text-gray-300 line-clamp-2">
                              {contact.message}
                            </p>
                            <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                              <span>提交时间: {moment(contact.created_at).format('YYYY-MM-DD HH:mm:ss')}</span>
                              {contact.replied_at && (
                                <span>回复时间: {moment(contact.replied_at).format('YYYY-MM-DD HH:mm:ss')}</span>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center gap-2 ml-4">
                            <Dialog>
                              <DialogTrigger asChild>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    setSelectedContact(contact);
                                    handleMarkAsRead(contact);
                                  }}
                                >
                                  <Eye className="w-4 h-4 mr-1" />
                                  查看
                                </Button>
                              </DialogTrigger>
                              <DialogContent className="max-w-2xl">
                                <DialogHeader>
                                  <DialogTitle>联系详情</DialogTitle>
                                  <DialogDescription>
                                    查看和回复用户消息
                                  </DialogDescription>
                                </DialogHeader>
                                {selectedContact && (
                                  <div className="space-y-4">
                                    <div className="grid grid-cols-2 gap-4">
                                      <div>
                                        <label className="text-sm font-medium">姓名</label>
                                        <p className="text-sm text-gray-600">{selectedContact.name}</p>
                                      </div>
                                      <div>
                                        <label className="text-sm font-medium">邮箱</label>
                                        <p className="text-sm text-gray-600">{selectedContact.email}</p>
                                      </div>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium">主题</label>
                                      <p className="text-sm text-gray-600">{selectedContact.subject}</p>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium">消息内容</label>
                                      <p className="text-sm text-gray-600 whitespace-pre-wrap bg-gray-50 dark:bg-gray-800 p-3 rounded">
                                        {selectedContact.message}
                                      </p>
                                    </div>
                                    {selectedContact.admin_reply && (
                                      <div>
                                        <label className="text-sm font-medium">管理员回复</label>
                                        <p className="text-sm text-gray-600 whitespace-pre-wrap bg-blue-50 dark:bg-blue-900/20 p-3 rounded">
                                          {selectedContact.admin_reply}
                                        </p>
                                        <p className="text-xs text-gray-500 mt-1">
                                          回复时间: {moment(selectedContact.replied_at).format('YYYY-MM-DD HH:mm:ss')}
                                          {selectedContact.replied_by && ` | 回复人: ${selectedContact.replied_by}`}
                                        </p>
                                      </div>
                                    )}
                                    <div>
                                      <label className="text-sm font-medium">回复消息</label>
                                      <Textarea
                                        value={replyText}
                                        onChange={(e) => setReplyText(e.target.value)}
                                        placeholder="输入回复内容..."
                                        rows={4}
                                        className="mt-1"
                                      />
                                    </div>
                                    <div className="flex items-center gap-2">
                                      <Button
                                        onClick={handleReply}
                                        disabled={replying || !replyText.trim()}
                                        className="flex items-center gap-2"
                                      >
                                        <Reply className="w-4 h-4" />
                                        {replying ? '回复中...' : '发送回复'}
                                      </Button>
                                      <Select
                                        value={selectedContact.status}
                                        onValueChange={(status) => handleContactAction(selectedContact.id, 'update_status', { status })}
                                      >
                                        <SelectTrigger className="w-32">
                                          <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                          <SelectItem value="unread">未读</SelectItem>
                                          <SelectItem value="read">已读</SelectItem>
                                          <SelectItem value="replied">已回复</SelectItem>
                                          <SelectItem value="closed">已关闭</SelectItem>
                                        </SelectContent>
                                      </Select>
                                    </div>
                                  </div>
                                )}
                              </DialogContent>
                            </Dialog>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}

              {/* 分页 */}
              {totalPages > 1 && (
                <div className="flex items-center justify-center gap-2 mt-6">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPage(page - 1)}
                    disabled={page <= 1}
                  >
                    上一页
                  </Button>
                  <span className="text-sm text-gray-600">
                    第 {page} 页，共 {totalPages} 页
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPage(page + 1)}
                    disabled={page >= totalPages}
                  >
                    下一页
                  </Button>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
