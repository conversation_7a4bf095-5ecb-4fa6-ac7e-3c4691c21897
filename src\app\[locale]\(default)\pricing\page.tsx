'use client';

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Check, Star, Zap, Crown, Building2, Briefcase } from "lucide-react";
import { useAppContext } from "@/contexts/app";
import { toast } from "sonner";
import { useTranslations } from "next-intl";
import Script from 'next/script';

interface PricingPlan {
  id: string;
  name: string;
  description: string;
  credits: number;
  price: number;
  originalPrice?: number;
  currency: string;
  interval: 'month' | 'year' | 'one-time';
  features: string[];
  popular?: boolean;
  icon: React.ReactNode;
}

const getMonthlyPlans = (t: any): PricingPlan[] => [
  {
    id: "basic-monthly",
    name: t('plans.basic.name'),
    description: t('plans.basic.description', { credits: 100 }),
    credits: 100,
    price: 7.13,
    currency: "USD",
    interval: "month",
    features: [
      t('plans.basic.description', { credits: 100 }),
      t('price_per_credit', { price: '0.07' }),
      t('auto_renewal'),
      t('high_quality'),
      t('customer_support')
    ],
    icon: <Zap className="w-6 h-6" />,
  },
  {
    id: "standard-monthly",
    name: t('plans.standard.name'),
    description: t('plans.standard.description', { credits: 200 }),
    credits: 200,
    price: 10.19,
    currency: "USD",
    interval: "month",
    features: [
      t('plans.standard.description', { credits: 200 }),
      t('price_per_credit', { price: '0.05' }),
      t('auto_renewal'),
      t('batch_processing'),
      t('priority_processing')
    ],
    popular: true,
    icon: <Star className="w-6 h-6" />,
  },
  {
    id: "pro-monthly",
    name: t('plans.pro.name'),
    description: t('plans.pro.description', { credits: 500 }),
    credits: 500,
    price: 15.30,
    currency: "USD",
    interval: "month",
    features: [
      t('plans.pro.description', { credits: 500 }),
      t('price_per_credit', { price: '0.03' }),
      t('auto_renewal'),
      t('api_access'),
      t('customer_support')
    ],
    icon: <Crown className="w-6 h-6" />,
  },
  {
    id: "enterprise-monthly",
    name: t('plans.enterprise.name'),
    description: t('plans.enterprise.description', { credits: 1000 }),
    credits: 1000,
    price: 20.40,
    currency: "USD",
    interval: "month",
    features: [
      t('plans.enterprise.description', { credits: 1000 }),
      t('price_per_credit', { price: '0.02' }),
      t('auto_renewal'),
      t('features.team_management'),
      t('features.dedicated_support')
    ],
    icon: <Building2 className="w-6 h-6" />,
  },
];

const getYearlyPlans = (t: any): PricingPlan[] => [
  {
    id: "basic-yearly",
    name: t('plans.basic.name'),
    description: t('plans.basic.yearly_description', { credits: 100 }),
    credits: 1200,
    price: 45.91,
    originalPrice: 85.56,
    currency: "USD",
    interval: "year",
    features: [
      t('plans.basic.yearly_credits', { credits: 1200 }),
      t('price_per_credit', { price: '0.04' }),
      t('features.one_time_payment'),
      t('high_quality'),
      t('customer_support')
    ],
    icon: <Zap className="w-6 h-6" />,
  },
  {
    id: "standard-yearly",
    name: t('plans.standard.name'),
    description: t('plans.standard.yearly_description', { credits: 200 }),
    credits: 2400,
    price: 71.43,
    originalPrice: 122.28,
    currency: "USD",
    interval: "year",
    features: [
      t('plans.standard.yearly_credits', { credits: 2400 }),
      t('price_per_credit', { price: '0.03' }),
      t('features.one_time_payment'),
      t('batch_processing'),
      t('priority_processing')
    ],
    popular: true,
    icon: <Star className="w-6 h-6" />,
  },
  {
    id: "pro-yearly",
    name: t('plans.pro.name'),
    description: t('plans.pro.yearly_description', { credits: 500 }),
    credits: 6000,
    price: 101.97,
    originalPrice: 183.60,
    currency: "USD",
    interval: "year",
    features: [
      t('plans.pro.yearly_credits', { credits: 6000 }),
      t('price_per_credit', { price: '0.02' }),
      t('features.one_time_payment'),
      t('api_access'),
      t('features.advanced_support')
    ],
    icon: <Crown className="w-6 h-6" />,
  },
  {
    id: "enterprise-yearly",
    name: t('plans.enterprise.name'),
    description: t('plans.enterprise.yearly_description', { credits: 1000 }),
    credits: 12000,
    price: 142.86,
    originalPrice: 244.80,
    currency: "USD",
    interval: "year",
    features: [
      t('plans.enterprise.yearly_credits', { credits: 12000 }),
      t('price_per_credit', { price: '0.01' }),
      t('features.one_time_payment'),
      t('features.team_management'),
      t('features.dedicated_support')
    ],
    icon: <Building2 className="w-6 h-6" />,
  },
];

const getOneTimePlans = (t: any): PricingPlan[] => [
  {
    id: "starter-pack",
    name: t('packages.starter'),
    description: t('packages.starter_description', { credits: 50 }),
    credits: 50,
    price: 8.15,
    currency: "USD",
    interval: "one-time",
    features: [
      t('packages.starter_credits', { credits: 50 }),
      t('price_per_credit', { price: '0.16' }),
      t('features.never_expire'),
      t('features.no_auto_renewal'),
      t('high_quality')
    ],
    icon: <Zap className="w-6 h-6" />,
  },
  {
    id: "value-pack",
    name: t('packages.value'),
    description: t('packages.value_description', { credits: 200 }),
    credits: 200,
    price: 25.50,
    currency: "USD",
    interval: "one-time",
    features: [
      t('packages.value_credits', { credits: 200 }),
      t('price_per_credit', { price: '0.13' }),
      t('features.never_expire'),
      t('features.no_auto_renewal'),
      t('batch_processing')
    ],
    popular: true,
    icon: <Star className="w-6 h-6" />,
  },
  {
    id: "pro-pack",
    name: t('packages.professional'),
    description: t('packages.professional_description', { credits: 500 }),
    credits: 500,
    price: 56.12,
    currency: "USD",
    interval: "one-time",
    features: [
      t('packages.professional_credits', { credits: 500 }),
      t('price_per_credit', { price: '0.11' }),
      t('features.never_expire'),
      t('features.no_auto_renewal'),
      t('priority_processing')
    ],
    icon: <Crown className="w-6 h-6" />,
  },
  {
    id: "mega-pack",
    name: t('packages.mega'),
    description: t('packages.mega_description', { credits: 1000 }),
    credits: 1000,
    price: 86.73,
    currency: "USD",
    interval: "one-time",
    features: [
      t('packages.mega_credits', { credits: 1000 }),
      t('price_per_credit', { price: '0.09' }),
      t('features.never_expire'),
      t('features.no_auto_renewal'),
      t('api_access')
    ],
    icon: <Building2 className="w-6 h-6" />,
  },
];

const PricingPage = () => {
  const [planType, setPlanType] = useState<'monthly' | 'yearly' | 'one-time'>('yearly');
  const [isLoading, setIsLoading] = useState(false);
  const [loadingPlanId, setLoadingPlanId] = useState<string | null>(null);
  const { user } = useAppContext();
  const t = useTranslations('pricing');

  // 动态生成计划数据
  const monthlyPlans = getMonthlyPlans(t);
  const yearlyPlans = getYearlyPlans(t);
  const oneTimePlans = getOneTimePlans(t);

  const handleSubscribe = async (plan: PricingPlan) => {
    if (!user) {
      window.location.href = '/auth/signin?callbackUrl=' + encodeURIComponent(window.location.pathname);
      return;
    }

    setIsLoading(true);
    setLoadingPlanId(plan.id);

    try {
      // Show loading message for PayPal
      toast.loading(t('payment.creating_session'), { id: 'payment-loading' });

      const response = await fetch('/api/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          plan_id: plan.id,
          credits: plan.credits,
          price: plan.price,
          currency: plan.currency,
          interval: plan.interval,
          valid_months: plan.interval === 'year' ? 12 : plan.interval === 'month' ? 1 : 0,
        }),
      });

      if (response.status === 401) {
        toast.dismiss('payment-loading');
        window.location.href = '/auth/signin?callbackUrl=' + encodeURIComponent(window.location.pathname);
        return;
      }

      if (!response.ok) {
        toast.dismiss('payment-loading');
        throw new Error(t('payment.payment_failed'));
      }

      const data = await response.json();
      if (data.code === 0) {
        toast.dismiss('payment-loading');

        if (data.data.redirect_url) {
          // PayPal payment - show redirect message
          toast.success(t('payment.redirecting_paypal'), { duration: 2000 });
          setTimeout(() => {
            window.location.href = data.data.redirect_url;
          }, 1000);
        } else if (data.data.payment_url) {
          // Legacy payment URL support
          toast.success(t('payment.redirecting_payment'), { duration: 2000 });
          setTimeout(() => {
            window.location.href = data.data.payment_url;
          }, 1000);
        } else {
          // Development environment success
          toast.success(`${t('subscription_successful')} ${plan.credits} ${t('credits')}`);
          setTimeout(() => {
            window.location.reload();
          }, 1500);
        }
      } else {
        toast.dismiss('payment-loading');
        throw new Error(data.message || t('payment_request_failed'));
      }
    } catch (error) {
      console.error('Subscribe error:', error);
      toast.dismiss('payment-loading');
      toast.error(error instanceof Error ? error.message : t('payment_request_failed_retry'));
    } finally {
      setIsLoading(false);
      setLoadingPlanId(null);
    }
  };

  const getCurrentPlans = () => {
    switch (planType) {
      case 'monthly':
        return monthlyPlans;
      case 'yearly':
        return yearlyPlans;
      case 'one-time':
        return oneTimePlans;
      default:
        return yearlyPlans;
    }
  };

  // Plan Type Selector badges
  const planTypeBadges = {
    monthly: null,
    yearly: t('badges.most_cost_effective'),
    'one-time': t('badges.buyout'),
  };

  // Plan interval label
  const getIntervalLabel = (interval: string) => {
    if (interval === 'month') return t('intervals.per_month');
    if (interval === 'year') return t('intervals.per_year');
    if (interval === 'one-time') return t('intervals.one_time');
    return '';
  };

  // Credits label
  const creditsLabel = t('intervals.credits');

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <header className="w-full py-6 px-4 border-b border-white/10 backdrop-blur-sm bg-black/20">
        <div className="max-w-7xl mx-auto flex justify-center items-center">
          <img src="/logo.png" alt="Watermark Remover" className="h-10 mr-3" />
          <span className="text-2xl font-bold tracking-tight text-white">{t('title')}</span>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 py-16">
        {/* Title Section */}
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold mb-6 text-white">
            {t('title')}
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
            {t('subtitle')}
          </p>

          {/* Free Plan Info */}
          <div className="bg-gradient-to-r from-green-500/20 to-blue-500/20 backdrop-blur-sm rounded-2xl p-6 border border-green-500/30 max-w-2xl mx-auto">
            <div className="flex items-center justify-center mb-4">
              <div className="bg-gradient-to-r from-green-500 to-blue-500 p-3 rounded-2xl">
                <Zap className="w-6 h-6 text-white" />
              </div>
            </div>
            <h3 className="text-2xl font-bold text-white mb-2">{t('free_plan.title')}</h3>
            <p className="text-gray-300 mb-4">
              {t('free_plan.description')}
            </p>
            <div className="text-sm text-gray-400">
            {t('free_plan.benefits')}
            </div>
          </div>
        </div>

        {/* Plan Type Selector */}
        <div className="flex justify-center mb-12">
          <div className="bg-black/30 backdrop-blur-sm rounded-2xl p-2 border border-white/10">
            <div className="flex space-x-2">
              {[
                { key: 'monthly', label: `💳 ${t('monthly')}`, badge: planTypeBadges.monthly },
                { key: 'yearly', label: `🗓️ ${t('yearly')}`, badge: planTypeBadges.yearly },
                { key: 'one-time', label: `💰 ${t('one_time')}`, badge: planTypeBadges['one-time'] },
              ].map((option) => (
                <button
                  key={option.key}
                  onClick={() => setPlanType(option.key as any)}
                  className={`relative px-6 py-3 rounded-xl font-semibold transition-all duration-200 ${
                    planType === option.key
                      ? 'bg-white text-gray-900 shadow-lg'
                      : 'text-gray-300 hover:text-white hover:bg-white/10'
                  }`}
                >
                  {option.label}
                  {option.badge && (
                    <Badge className="absolute -top-2 -right-2 bg-gradient-to-r from-green-400 to-blue-500 text-white text-xs">
                      {option.badge}
                    </Badge>
                  )}
                </button>
              ))}
            </div>
          </div>
        </div>
        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-12">
          {getCurrentPlans().map((plan) => (
            <div
              key={plan.id}
              className={`relative bg-black/40 backdrop-blur-sm rounded-2xl p-6 border transition-all duration-300 hover:scale-105 hover:shadow-2xl ${
                plan.popular
                  ? 'border-purple-500 shadow-purple-500/20 shadow-2xl'
                  : 'border-white/10 hover:border-white/20'
              }`}
            >
              {/* Popular Badge */}
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-3 py-1 text-xs font-semibold">
                    {t('popular')}
                  </Badge>
                </div>
              )}

              {/* Plan Header */}
              <div className="text-center mb-6">
                <div className="flex justify-center mb-3">
                  <div className={`p-2 rounded-xl ${
                    plan.popular
                      ? 'bg-gradient-to-r from-purple-500 to-pink-500'
                      : 'bg-gradient-to-r from-blue-500 to-cyan-500'
                  }`}>
                    {plan.icon}
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">{plan.name}</h3>
                <p className="text-gray-400 mb-3 text-sm">{plan.description}</p>

                {/* Price */}
                <div className="mb-4">
                  {plan.originalPrice && (
                    <div className="text-gray-500 line-through text-base mb-1">
                      ${plan.originalPrice}
                    </div>
                  )}
                  <div className="text-3xl font-bold text-white mb-1">
                    ${plan.price}
                  </div>
                  <div className="text-gray-400 text-xs">
                    {getIntervalLabel(plan.interval)}
                  </div>
                </div>

                {/* Credits */}
                <div className="bg-white/5 rounded-lg p-2 mb-4">
                  <div className="text-xl font-bold text-white">{plan.credits}</div>
                  <div className="text-gray-400 text-xs">{creditsLabel}</div>
                </div>
              </div>

              {/* Features */}
              <div className="mb-6">
                <ul className="space-y-2">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-center text-gray-300 text-sm">
                      <Check className="w-4 h-4 text-green-400 mr-2 flex-shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Subscribe Button */}
              <Button
                onClick={() => handleSubscribe(plan)}
                disabled={isLoading}
                className={`w-full py-3 px-4 text-sm font-semibold rounded-lg transition-all duration-200 ${
                  plan.popular
                    ? 'bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white shadow-lg'
                    : 'bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white shadow-md'
                } ${isLoading && loadingPlanId === plan.id ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                {isLoading && loadingPlanId === plan.id ? (
                  <div className="flex items-center justify-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                    <span>{t('payment.creating_session')}</span>
                  </div>
                ) : (
                  <div className="flex items-center justify-center">
                    <span>
                      {t('subscribe_with_paypal')}
                    </span>
                  </div>
                )}
              </Button>
            </div>
          ))}
        </div>

        {/* Payment Methods */}
        <div className="text-center">
          <div className="mb-6">
            <h3 className="text-xl font-semibold text-white mb-4">{t('payment.secure_payment')}</h3>
            <div className="flex items-center justify-center gap-4">
              {/* PayPal Payment Method - Highlighted as Primary */}
              <div className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-sm rounded-xl p-6 border border-blue-500/30 relative">
                <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-gradient-to-r from-blue-500 to-purple-500 text-white px-2 py-1 text-xs">
                    {t('badges.recommended')}
                  </Badge>
                </div>
                <div className="text-white text-lg font-bold mb-2 flex items-center justify-center gap-2">
                  <svg className="w-6 h-6" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h7.46c2.57 0 4.578.543 5.69 1.81 1.01 1.15 1.304 2.42 1.012 4.287-.023.143-.047.288-.077.437-.983 5.05-4.349 6.797-8.647 6.797h-2.19c-.524 0-.968.382-1.05.9l-1.12 7.106zm14.146-14.42a3.35 3.35 0 0 0-.607-.421c-.315-.178-.7-.284-1.139-.284H12.12l-.98 6.22h2.426c2.963 0 4.307-1.432 4.307-4.307 0-.69-.12-1.208-.651-1.208z"/>
                  </svg>
                  PayPal
                </div>
                <div className="text-white text-sm font-medium">{t('payment.paypal_trusted')}</div>
                <div className="text-gray-300 text-xs">{t('payment.paypal_secure')}</div>
              </div>
              {/* Alternative Payment Methods */}
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/10 opacity-60">
                <div className="text-white font-semibold text-sm">{t('payment.credit_card')}</div>
                <div className="text-gray-400 text-xs">Visa • MasterCard</div>
              </div>
            </div>
            {/* Payment Features */}
            <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
              <div className="flex items-center justify-center gap-2 text-gray-300 text-sm">
                <Check className="w-4 h-4 text-green-400" />
                <span>{t('payment.ssl_protection')}</span>
              </div>
              <div className="flex items-center justify-center gap-2 text-gray-300 text-sm">
                <Check className="w-4 h-4 text-green-400" />
                <span>{t('payment.money_back')}</span>
              </div>
              <div className="flex items-center justify-center gap-2 text-gray-300 text-sm">
                <Check className="w-4 h-4 text-green-400" />
                <span>{t('payment.customer_support_24_7')}</span>
              </div>
            </div>
          </div>
          <p className="text-gray-400 text-sm">
            {t('payment.payment_security_note')}
          </p>
        </div>
      </main>

      {/* 结构化数据 */}
      <Script
        id="pricing-structured-data"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Product",
            "name": "Watermark Remover",
            "description": "Professional AI-powered watermark removal tool",
            "brand": {
              "@type": "Brand",
              "name": "Watermark Remover"
            },
            "offers": [
              {
                "@type": "Offer",
                "name": "Basic Monthly Plan",
                "description": "100 credits per month",
                "price": "7.13",
                "priceCurrency": "USD",
                "priceValidUntil": "2025-12-31",
                "availability": "https://schema.org/InStock",
                "url": `${process.env.NEXT_PUBLIC_WEB_URL}/pricing`,
                "seller": {
                  "@type": "Organization",
                  "name": "Watermark Remover"
                }
              },
              {
                "@type": "Offer",
                "name": "Standard Monthly Plan",
                "description": "200 credits per month",
                "price": "10.19",
                "priceCurrency": "USD",
                "priceValidUntil": "2025-12-31",
                "availability": "https://schema.org/InStock",
                "url": `${process.env.NEXT_PUBLIC_WEB_URL}/pricing`,
                "seller": {
                  "@type": "Organization",
                  "name": "Watermark Remover"
                }
              }
            ]
          })
        }}
      />
    </div>
  );
};

export default PricingPage;
