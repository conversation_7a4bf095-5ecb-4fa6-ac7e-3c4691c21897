import { respData, respErr } from "@/lib/resp";
import { isCurrentUserAdmin } from "@/services/admin";
import { db } from "@/db";
import { credits, users, orders } from "@/db/schema";
import { sql, eq, and, gte, sum, count, desc, or, isNull } from "drizzle-orm";

/**
 * 获取积分统计数据
 */
export async function GET(req: Request) {
  try {
    // 检查管理员权限
    const isAdmin = await isCurrentUserAdmin();
    if (!isAdmin) {
      return respErr("无权限访问", 403);
    }

    // 获取真实的积分统计数据
    const [
      totalStats,
      validStats,
      usedStats,
      byTypeStats,
      userRankingStats,
      subscriptionStats
    ] = await Promise.all([
      // 总积分统计
      db().select({
        totalCredits: sum(credits.credits),
        totalTransactions: count(credits.id)
      }).from(credits),

      // 有效积分统计（正数积分）
      db().select({
        validCredits: sum(credits.credits),
        validTransactions: count(credits.id)
      }).from(credits).where(gte(credits.credits, 0)),

      // 已使用积分统计（负数积分）
      db().select({
        usedCredits: sum(credits.credits),
        usedTransactions: count(credits.id)
      }).from(credits).where(sql`${credits.credits} < 0`),

      // 按类型统计
      db().select({
        transType: credits.trans_type,
        totalCredits: sum(credits.credits),
        transactionCount: count(credits.id)
      }).from(credits).groupBy(credits.trans_type),

      // 用户积分排行榜（前20名）
      db().select({
        userUuid: credits.user_uuid,
        totalCredits: sum(credits.credits),
        transactionCount: count(credits.id)
      }).from(credits)
        .groupBy(credits.user_uuid)
        .orderBy(desc(sum(credits.credits)))
        .limit(20),

      // 订阅相关统计
      Promise.all([
        // 订阅积分
        db().select({
          totalCredits: sum(credits.credits),
          transactionCount: count(credits.id)
        }).from(credits).where(eq(credits.trans_type, 'order_pay')),

        // 月度赠送积分
        db().select({
          totalCredits: sum(credits.credits),
          transactionCount: count(credits.id)
        }).from(credits).where(eq(credits.trans_type, 'monthly_gift')),

        // 新用户积分
        db().select({
          totalCredits: sum(credits.credits),
          transactionCount: count(credits.id)
        }).from(credits).where(eq(credits.trans_type, 'new_user'))
      ])
    ]);

    // 获取用户信息用于排行榜
    const userUuids = userRankingStats.map(stat => stat.userUuid);
    const usersInfo = userUuids.length > 0 ? await db()
      .select({
        uuid: users.uuid,
        email: users.email,
        nickname: users.nickname
      })
      .from(users)
      .where(sql`${users.uuid} IN (${userUuids.map(uuid => `'${uuid}'`).join(',')})`) : [];

    // 组装数据
    const overview = {
      totalCredits: Number(totalStats[0]?.totalCredits || 0),
      totalTransactions: Number(totalStats[0]?.totalTransactions || 0),
      validCredits: Number(validStats[0]?.validCredits || 0),
      validTransactions: Number(validStats[0]?.validTransactions || 0),
      usedCredits: Math.abs(Number(usedStats[0]?.usedCredits || 0)),
      usedTransactions: Number(usedStats[0]?.usedTransactions || 0),
    };

    const byType = byTypeStats.map(stat => ({
      transType: stat.transType,
      totalCredits: Number(stat.totalCredits || 0),
      transactionCount: Number(stat.transactionCount || 0),
    }));

    const userRanking = userRankingStats
      .filter(stat => Number(stat.totalCredits) > 0)
      .map(stat => {
        const userInfo = usersInfo.find(user => user.uuid === stat.userUuid);
        return {
          userUuid: stat.userUuid,
          userEmail: userInfo?.email || '未知用户',
          userName: userInfo?.nickname || '',
          totalCredits: Number(stat.totalCredits || 0),
          transactionCount: Number(stat.transactionCount || 0),
        };
      });

    const subscription = {
      totalSubscriptionCredits: Number(subscriptionStats[0][0]?.totalCredits || 0),
      subscriptionTransactions: Number(subscriptionStats[0][0]?.transactionCount || 0),
      totalOneTimeCredits: Number(subscriptionStats[0][0]?.totalCredits || 0), // 暂时使用相同数据
      oneTimeTransactions: Number(subscriptionStats[0][0]?.transactionCount || 0),
      totalGiftCredits: Number(subscriptionStats[1][0]?.totalCredits || 0),
      giftTransactions: Number(subscriptionStats[1][0]?.transactionCount || 0),
    };

    const stats = {
      overview,
      byType,
      userRanking,
      subscription,
    };

    return respData(stats);

  } catch (e) {
    console.log("get credits stats failed: ", e);
    // 如果数据库查询失败，返回空数据而不是错误
    const emptyStats = {
      overview: {
        totalCredits: 0,
        totalTransactions: 0,
        validCredits: 0,
        validTransactions: 0,
        usedCredits: 0,
        usedTransactions: 0,
      },
      byType: [],
      userRanking: [],
      subscription: {
        totalSubscriptionCredits: 0,
        subscriptionTransactions: 0,
        totalOneTimeCredits: 0,
        oneTimeTransactions: 0,
        totalGiftCredits: 0,
        giftTransactions: 0,
      },
    };
    return respData(emptyStats);
  }
}
