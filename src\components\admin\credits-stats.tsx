'use client'

import { useState, useEffect, memo, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Coins, TrendingUp, Users, Gift, ShoppingCart, Zap } from 'lucide-react'
import { toast } from 'sonner'

interface CreditsStats {
  overview: {
    totalCredits: number
    totalTransactions: number
    validCredits: number
    validTransactions: number
    usedCredits: number
    usedTransactions: number
  }
  byType: Array<{
    transType: string
    totalCredits: number
    transactionCount: number
  }>
  userRanking: Array<{
    userUuid: string
    userEmail: string
    userName: string
    totalCredits: number
    transactionCount: number
  }>
  subscription: {
    totalSubscriptionCredits: number
    subscriptionTransactions: number
    totalOneTimeCredits: number
    oneTimeTransactions: number
    totalGiftCredits: number
    giftTransactions: number
  }
}

const transTypeNames: Record<string, string> = {
  'purchase': '购买积分',
  'monthly_gift': '月度赠送',
  'ping': 'Ping消费',
  'remove_watermark': '去水印消费',
  'subscription': '订阅积分',
  'one_time': '一次性购买',
}

const transTypeIcons: Record<string, any> = {
  'purchase': ShoppingCart,
  'monthly_gift': Gift,
  'ping': Zap,
  'remove_watermark': Zap,
  'subscription': TrendingUp,
  'one_time': Coins,
}

// 简单的缓存机制
let creditsCache: { data: CreditsStats; timestamp: number } | null = null
const CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

const CreditsStats = memo(function CreditsStats() {
  const [stats, setStats] = useState<CreditsStats | null>(null)
  const [loading, setLoading] = useState(true)

  // 获取积分统计
  const fetchStats = useCallback(async () => {
    try {
      setLoading(true)

      // 检查缓存
      if (creditsCache && Date.now() - creditsCache.timestamp < CACHE_DURATION) {
        setStats(creditsCache.data)
        setLoading(false)
        return
      }

      const response = await fetch('/api/admin/credits-stats')
      const data = await response.json()

      if (data.code === 0) {
        setStats(data.data)
        // 更新缓存
        creditsCache = {
          data: data.data,
          timestamp: Date.now()
        }
      } else {
        toast.error(data.message || '获取积分统计失败')
      }
    } catch (error) {
      console.error('Failed to fetch credits stats:', error)
      toast.error('获取积分统计失败')
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchStats()
  }, [fetchStats])

  if (loading) {
    return (
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {[...Array(6)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (!stats) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-gray-500">
            无法加载积分统计数据
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* 总览统计 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总积分</CardTitle>
            <Coins className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.overview.totalCredits.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              {stats.overview.totalTransactions} 笔交易
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">有效积分</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {stats.overview.validCredits.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              {stats.overview.validTransactions} 笔有效交易
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已使用积分</CardTitle>
            <Zap className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {stats.overview.usedCredits.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              {stats.overview.usedTransactions} 笔消费交易
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 订阅统计 */}
      <Card>
        <CardHeader>
          <CardTitle>订阅积分统计</CardTitle>
          <CardDescription>不同类型的积分来源统计</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <TrendingUp className="w-4 h-4 text-blue-600" />
                <span className="text-sm font-medium">订阅积分</span>
              </div>
              <div className="text-xl font-bold text-blue-600">
                {stats.subscription.totalSubscriptionCredits.toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">
                {stats.subscription.subscriptionTransactions} 笔订阅
              </p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <ShoppingCart className="w-4 h-4 text-purple-600" />
                <span className="text-sm font-medium">一次性购买</span>
              </div>
              <div className="text-xl font-bold text-purple-600">
                {stats.subscription.totalOneTimeCredits.toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">
                {stats.subscription.oneTimeTransactions} 笔购买
              </p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Gift className="w-4 h-4 text-green-600" />
                <span className="text-sm font-medium">月度赠送</span>
              </div>
              <div className="text-xl font-bold text-green-600">
                {stats.subscription.totalGiftCredits.toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">
                {stats.subscription.giftTransactions} 笔赠送
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 按类型统计 */}
      <Card>
        <CardHeader>
          <CardTitle>交易类型统计</CardTitle>
          <CardDescription>按交易类型分组的积分统计</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {stats.byType.map((item) => {
              const Icon = transTypeIcons[item.transType] || Coins
              return (
                <div key={item.transType} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <Icon className="w-4 h-4 text-gray-600" />
                    <span className="font-medium">
                      {transTypeNames[item.transType] || item.transType}
                    </span>
                  </div>
                  <div className="text-right">
                    <div className="font-bold">
                      {item.totalCredits.toLocaleString()}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {item.transactionCount} 笔
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* 用户积分排行榜 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            用户积分排行榜
          </CardTitle>
          <CardDescription>积分最多的前20名用户</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {stats.userRanking.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                暂无数据
              </div>
            ) : (
              stats.userRanking.map((user, index) => (
                <div key={user.userUuid} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <Badge variant={index < 3 ? "default" : "secondary"} className="w-8 h-6 flex items-center justify-center">
                      {index + 1}
                    </Badge>
                    <div>
                      <div className="font-medium">
                        {user.userName || user.userEmail}
                      </div>
                      {user.userName && (
                        <div className="text-xs text-muted-foreground">
                          {user.userEmail}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-bold">
                      {user.totalCredits.toLocaleString()}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {user.transactionCount} 笔交易
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
})

export default CreditsStats
