'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { 
  Settings, 
  Globe, 
  Mail, 
  CreditCard, 
  Shield, 
  Database,
  Save,
  RefreshCw
} from "lucide-react";
import { toast } from "sonner";

export default function SettingsPage() {
  const [loading, setLoading] = useState(false);
  const [settings, setSettings] = useState({
    // 网站基本设置
    siteName: '水印去除器',
    siteDescription: '专业的AI水印去除工具',
    siteUrl: 'https://watermarkremover.top',
    contactEmail: '<EMAIL>',
    
    // 邮件设置
    smtpHost: '',
    smtpPort: '587',
    smtpUser: '',
    smtpPassword: '',
    smtpSecure: true,
    
    // 支付设置
    stripePublicKey: '',
    stripeSecretKey: '',
    paypalClientId: '',
    paypalClientSecret: '',
    
    // 系统设置
    enableRegistration: true,
    enableGoogleAuth: true,
    enableEmailVerification: false,
    maxFileSize: '10',
    allowedFileTypes: 'jpg,jpeg,png,webp',
    
    // API设置
    openaiApiKey: '',
    maxCreditsPerUser: '100',
    defaultCredits: '10',
  });

  const handleInputChange = (key: string, value: string | boolean) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSave = async (section: string) => {
    setLoading(true);
    try {
      // 这里应该调用API保存设置
      await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟API调用
      toast.success(`${section}设置已保存`);
    } catch (error) {
      toast.error('保存失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">系统设置</h1>
          <p className="text-gray-600 dark:text-gray-400">
            配置系统参数和功能设置
          </p>
        </div>
      </div>

      <Tabs defaultValue="general" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="general" className="flex items-center gap-2">
            <Globe className="w-4 h-4" />
            基本设置
          </TabsTrigger>
          <TabsTrigger value="email" className="flex items-center gap-2">
            <Mail className="w-4 h-4" />
            邮件配置
          </TabsTrigger>
          <TabsTrigger value="payment" className="flex items-center gap-2">
            <CreditCard className="w-4 h-4" />
            支付配置
          </TabsTrigger>
          <TabsTrigger value="system" className="flex items-center gap-2">
            <Shield className="w-4 h-4" />
            系统配置
          </TabsTrigger>
          <TabsTrigger value="api" className="flex items-center gap-2">
            <Database className="w-4 h-4" />
            API配置
          </TabsTrigger>
        </TabsList>

        {/* 基本设置 */}
        <TabsContent value="general">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="w-5 h-5" />
                网站基本信息
              </CardTitle>
              <CardDescription>
                配置网站的基本信息和联系方式
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="siteName">网站名称</Label>
                  <Input
                    id="siteName"
                    value={settings.siteName}
                    onChange={(e) => handleInputChange('siteName', e.target.value)}
                    placeholder="输入网站名称"
                  />
                </div>
                <div>
                  <Label htmlFor="siteUrl">网站地址</Label>
                  <Input
                    id="siteUrl"
                    value={settings.siteUrl}
                    onChange={(e) => handleInputChange('siteUrl', e.target.value)}
                    placeholder="https://example.com"
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="siteDescription">网站描述</Label>
                <Textarea
                  id="siteDescription"
                  value={settings.siteDescription}
                  onChange={(e) => handleInputChange('siteDescription', e.target.value)}
                  placeholder="输入网站描述"
                  rows={3}
                />
              </div>
              
              <div>
                <Label htmlFor="contactEmail">联系邮箱</Label>
                <Input
                  id="contactEmail"
                  type="email"
                  value={settings.contactEmail}
                  onChange={(e) => handleInputChange('contactEmail', e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>

              <Separator />
              
              <div className="flex justify-end">
                <Button 
                  onClick={() => handleSave('基本信息')}
                  disabled={loading}
                  className="flex items-center gap-2"
                >
                  <Save className="w-4 h-4" />
                  {loading ? '保存中...' : '保存设置'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 邮件配置 */}
        <TabsContent value="email">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="w-5 h-5" />
                SMTP邮件配置
              </CardTitle>
              <CardDescription>
                配置SMTP服务器用于发送系统邮件
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="smtpHost">SMTP服务器</Label>
                  <Input
                    id="smtpHost"
                    value={settings.smtpHost}
                    onChange={(e) => handleInputChange('smtpHost', e.target.value)}
                    placeholder="smtp.gmail.com"
                  />
                </div>
                <div>
                  <Label htmlFor="smtpPort">端口</Label>
                  <Input
                    id="smtpPort"
                    value={settings.smtpPort}
                    onChange={(e) => handleInputChange('smtpPort', e.target.value)}
                    placeholder="587"
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="smtpUser">用户名</Label>
                  <Input
                    id="smtpUser"
                    value={settings.smtpUser}
                    onChange={(e) => handleInputChange('smtpUser', e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <Label htmlFor="smtpPassword">密码</Label>
                  <Input
                    id="smtpPassword"
                    type="password"
                    value={settings.smtpPassword}
                    onChange={(e) => handleInputChange('smtpPassword', e.target.value)}
                    placeholder="应用专用密码"
                  />
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch
                  id="smtpSecure"
                  checked={settings.smtpSecure}
                  onCheckedChange={(checked) => handleInputChange('smtpSecure', checked)}
                />
                <Label htmlFor="smtpSecure">启用SSL/TLS加密</Label>
              </div>

              <Separator />
              
              <div className="flex justify-end gap-2">
                <Button variant="outline" className="flex items-center gap-2">
                  <RefreshCw className="w-4 h-4" />
                  测试连接
                </Button>
                <Button 
                  onClick={() => handleSave('邮件配置')}
                  disabled={loading}
                  className="flex items-center gap-2"
                >
                  <Save className="w-4 h-4" />
                  {loading ? '保存中...' : '保存设置'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 支付配置 */}
        <TabsContent value="payment">
          <div className="space-y-6">
            {/* Stripe配置 */}
            <Card>
              <CardHeader>
                <CardTitle>Stripe配置</CardTitle>
                <CardDescription>配置Stripe支付网关</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="stripePublicKey">公钥 (Publishable Key)</Label>
                  <Input
                    id="stripePublicKey"
                    value={settings.stripePublicKey}
                    onChange={(e) => handleInputChange('stripePublicKey', e.target.value)}
                    placeholder="pk_test_..."
                  />
                </div>
                <div>
                  <Label htmlFor="stripeSecretKey">私钥 (Secret Key)</Label>
                  <Input
                    id="stripeSecretKey"
                    type="password"
                    value={settings.stripeSecretKey}
                    onChange={(e) => handleInputChange('stripeSecretKey', e.target.value)}
                    placeholder="sk_test_..."
                  />
                </div>
              </CardContent>
            </Card>

            {/* PayPal配置 */}
            <Card>
              <CardHeader>
                <CardTitle>PayPal配置</CardTitle>
                <CardDescription>配置PayPal支付网关</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="paypalClientId">客户端ID</Label>
                  <Input
                    id="paypalClientId"
                    value={settings.paypalClientId}
                    onChange={(e) => handleInputChange('paypalClientId', e.target.value)}
                    placeholder="PayPal Client ID"
                  />
                </div>
                <div>
                  <Label htmlFor="paypalClientSecret">客户端密钥</Label>
                  <Input
                    id="paypalClientSecret"
                    type="password"
                    value={settings.paypalClientSecret}
                    onChange={(e) => handleInputChange('paypalClientSecret', e.target.value)}
                    placeholder="PayPal Client Secret"
                  />
                </div>

                <Separator />
                
                <div className="flex justify-end">
                  <Button 
                    onClick={() => handleSave('支付配置')}
                    disabled={loading}
                    className="flex items-center gap-2"
                  >
                    <Save className="w-4 h-4" />
                    {loading ? '保存中...' : '保存设置'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 系统配置 */}
        <TabsContent value="system">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="w-5 h-5" />
                系统功能配置
              </CardTitle>
              <CardDescription>
                配置系统功能开关和限制
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="enableRegistration">允许用户注册</Label>
                    <p className="text-sm text-gray-600">是否允许新用户注册账户</p>
                  </div>
                  <Switch
                    id="enableRegistration"
                    checked={settings.enableRegistration}
                    onCheckedChange={(checked) => handleInputChange('enableRegistration', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="enableGoogleAuth">Google登录</Label>
                    <p className="text-sm text-gray-600">是否启用Google OAuth登录</p>
                  </div>
                  <Switch
                    id="enableGoogleAuth"
                    checked={settings.enableGoogleAuth}
                    onCheckedChange={(checked) => handleInputChange('enableGoogleAuth', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="enableEmailVerification">邮箱验证</Label>
                    <p className="text-sm text-gray-600">是否要求用户验证邮箱</p>
                  </div>
                  <Switch
                    id="enableEmailVerification"
                    checked={settings.enableEmailVerification}
                    onCheckedChange={(checked) => handleInputChange('enableEmailVerification', checked)}
                  />
                </div>
              </div>

              <Separator />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="maxFileSize">最大文件大小 (MB)</Label>
                  <Input
                    id="maxFileSize"
                    value={settings.maxFileSize}
                    onChange={(e) => handleInputChange('maxFileSize', e.target.value)}
                    placeholder="10"
                  />
                </div>
                <div>
                  <Label htmlFor="allowedFileTypes">允许的文件类型</Label>
                  <Input
                    id="allowedFileTypes"
                    value={settings.allowedFileTypes}
                    onChange={(e) => handleInputChange('allowedFileTypes', e.target.value)}
                    placeholder="jpg,jpeg,png,webp"
                  />
                </div>
              </div>

              <Separator />
              
              <div className="flex justify-end">
                <Button 
                  onClick={() => handleSave('系统配置')}
                  disabled={loading}
                  className="flex items-center gap-2"
                >
                  <Save className="w-4 h-4" />
                  {loading ? '保存中...' : '保存设置'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* API配置 */}
        <TabsContent value="api">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="w-5 h-5" />
                API和积分配置
              </CardTitle>
              <CardDescription>
                配置第三方API和用户积分系统
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="openaiApiKey">OpenAI API密钥</Label>
                <Input
                  id="openaiApiKey"
                  type="password"
                  value={settings.openaiApiKey}
                  onChange={(e) => handleInputChange('openaiApiKey', e.target.value)}
                  placeholder="sk-..."
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="maxCreditsPerUser">用户最大积分</Label>
                  <Input
                    id="maxCreditsPerUser"
                    value={settings.maxCreditsPerUser}
                    onChange={(e) => handleInputChange('maxCreditsPerUser', e.target.value)}
                    placeholder="100"
                  />
                </div>
                <div>
                  <Label htmlFor="defaultCredits">新用户默认积分</Label>
                  <Input
                    id="defaultCredits"
                    value={settings.defaultCredits}
                    onChange={(e) => handleInputChange('defaultCredits', e.target.value)}
                    placeholder="10"
                  />
                </div>
              </div>

              <Separator />
              
              <div className="flex justify-end">
                <Button 
                  onClick={() => handleSave('API配置')}
                  disabled={loading}
                  className="flex items-center gap-2"
                >
                  <Save className="w-4 h-4" />
                  {loading ? '保存中...' : '保存设置'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
