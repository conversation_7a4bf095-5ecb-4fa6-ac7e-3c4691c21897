import { respData, respErr } from "@/lib/resp";
import { getFeedbacks, getFeedbacksTotal, getFeedbacksStats } from "@/models/feedback";
import { isCurrentUserAdmin } from "@/services/admin";

export async function POST(req: Request) {
  try {
    // 检查管理员权限
    const isAdmin = await isCurrentUserAdmin();
    if (!isAdmin) {
      return respErr("无权限访问", 403);
    }

    const { page = 1, limit = 20 } = await req.json();

    // 获取反馈列表
    const feedbacks = await getFeedbacks(page, limit);
    const total = await getFeedbacksTotal();

    return respData({
      feedbacks: feedbacks || [],
      total,
      page,
      limit,
      totalPages: Math.ceil((total || 0) / limit),
    });
  } catch (e) {
    console.log("get admin feedbacks failed: ", e);
    return respErr("获取反馈列表失败");
  }
}

export async function GET(req: Request) {
  try {
    // 检查管理员权限
    const isAdmin = await isCurrentUserAdmin();
    if (!isAdmin) {
      return respErr("无权限访问", 403);
    }

    // 获取反馈统计数据
    const stats = await getFeedbacksStats();

    return respData(stats);
  } catch (e) {
    console.log("get feedbacks stats failed: ", e);
    return respErr("获取反馈统计失败");
  }
}
