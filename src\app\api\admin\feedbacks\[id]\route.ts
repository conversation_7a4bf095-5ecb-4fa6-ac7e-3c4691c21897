import { respData, respErr } from "@/lib/resp";
import { findFeedbackById, updateFeedback, deleteFeedback } from "@/models/feedback";
import { isCurrentUserAdmin } from "@/services/admin";

export async function GET(req: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    // 检查管理员权限
    const isAdmin = await isCurrentUserAdmin();
    if (!isAdmin) {
      return respErr("无权限访问", 403);
    }

    const { id: idParam } = await params;
    const id = parseInt(idParam);
    if (isNaN(id)) {
      return respErr("无效的ID");
    }

    const feedback = await findFeedbackById(id);
    if (!feedback) {
      return respErr("反馈记录不存在");
    }

    return respData(feedback);
  } catch (e) {
    console.log("get feedback failed: ", e);
    return respErr("获取反馈记录失败");
  }
}

export async function PUT(req: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    // 检查管理员权限
    const isAdmin = await isCurrentUserAdmin();
    if (!isAdmin) {
      return respErr("无权限访问", 403);
    }

    const { id: idParam } = await params;
    const id = parseInt(idParam);
    if (isNaN(id)) {
      return respErr("无效的ID");
    }

    const { status } = await req.json();
    if (!status) {
      return respErr("状态不能为空");
    }

    const feedback = await updateFeedback(id, { status });
    if (!feedback) {
      return respErr("更新失败");
    }

    return respData(feedback);
  } catch (e) {
    console.log("update feedback failed: ", e);
    return respErr("更新反馈记录失败");
  }
}

export async function DELETE(req: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    // 检查管理员权限
    const isAdmin = await isCurrentUserAdmin();
    if (!isAdmin) {
      return respErr("无权限访问", 403);
    }

    const { id: idParam } = await params;
    const id = parseInt(idParam);
    if (isNaN(id)) {
      return respErr("无效的ID");
    }

    const success = await deleteFeedback(id);
    if (!success) {
      return respErr("删除失败");
    }

    return respData({ message: "删除成功" });
  } catch (e) {
    console.log("delete feedback failed: ", e);
    return respErr("删除反馈记录失败");
  }
}
