CREATE TABLE "system_logs" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "system_logs_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"level" varchar(20) DEFAULT 'info' NOT NULL,
	"action" varchar(100) NOT NULL,
	"message" text NOT NULL,
	"details" text,
	"user_uuid" varchar(255),
	"user_email" varchar(255),
	"ip_address" varchar(45),
	"user_agent" text,
	"module" varchar(50),
	"resource_id" varchar(255),
	"created_at" timestamp with time zone DEFAULT now()
);
