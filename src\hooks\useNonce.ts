'use client';

import { useEffect, useState } from 'react';

/**
 * Hook to get the nonce value from the response headers
 * This is used for CSP compliance when using inline scripts/styles
 */
export function useNonce(): string | null {
  const [nonce, setNonce] = useState<string | null>(null);

  useEffect(() => {
    // 尝试从meta标签获取nonce
    const metaNonce = document.querySelector('meta[name="csp-nonce"]')?.getAttribute('content');
    if (metaNonce) {
      setNonce(metaNonce);
      return;
    }

    // 如果没有meta标签，尝试从其他方式获取
    // 在实际应用中，nonce通常通过服务端渲染时注入
    const htmlElement = document.documentElement;
    const nonceAttr = htmlElement.getAttribute('data-nonce');
    if (nonceAttr) {
      setNonce(nonceAttr);
    }
  }, []);

  return nonce;
}

/**
 * 获取当前页面的nonce值
 * 这个函数可以在服务端组件中使用
 */
export function getNonceFromHeaders(): string | null {
  if (typeof window === 'undefined') {
    // 服务端环境，无法直接获取
    return null;
  }
  
  // 客户端环境，从meta标签获取
  const metaNonce = document.querySelector('meta[name="csp-nonce"]')?.getAttribute('content');
  return metaNonce || null;
}
