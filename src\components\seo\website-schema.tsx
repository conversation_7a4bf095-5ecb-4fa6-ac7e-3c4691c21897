'use client'

import Script from 'next/script'

export default function WebsiteSchema() {
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://watermarkremover.top'
  
  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Watermark Remover",
    "alternateName": "AI Watermark Removal Tool",
    "url": baseUrl,
    "description": "Professional AI-powered watermark removal tool. Remove watermarks from images quickly and easily with advanced AI technology.",
    "inLanguage": ["en", "zh", "fr", "pt", "ru"],
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${baseUrl}/search?q={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    },
    "publisher": {
      "@type": "Organization",
      "name": "Watermark Remover",
      "url": baseUrl,
      "logo": {
        "@type": "ImageObject",
        "url": `${baseUrl}/logo.png`,
        "width": 512,
        "height": 512
      }
    },
    "mainEntity": {
      "@type": "SoftwareApplication",
      "name": "Watermark Remover",
      "applicationCategory": "MultimediaApplication",
      "operatingSystem": "Web Browser",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD",
        "availability": "https://schema.org/InStock"
      },
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.8",
        "ratingCount": "1250",
        "bestRating": "5",
        "worstRating": "1"
      }
    }
  }

  return (
    <Script
      id="website-schema"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(websiteSchema)
      }}
    />
  )
}
