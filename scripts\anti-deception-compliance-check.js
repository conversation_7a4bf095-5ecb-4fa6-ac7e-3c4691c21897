#!/usr/bin/env node

/**
 * 反欺骗合规检查脚本
 * 专门检查网站是否符合谷歌反欺骗标准
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

const SITE_URL = process.env.NEXT_PUBLIC_WEB_URL || 'https://watermarkremover.top';

console.log('🛡️ 开始反欺骗合规检查...');
console.log(`检查网站: ${SITE_URL}`);

// 1. 检查透明度合规
function checkTransparencyCompliance(siteUrl) {
  return new Promise((resolve) => {
    console.log('\n🔍 检查透明度合规:');
    
    const req = https.get(siteUrl, (res) => {
      let html = '';
      res.on('data', chunk => html += chunk);
      res.on('end', () => {
        const checks = {
          hasLoginExplanation: false,
          hasGoogleIdentification: false,
          hasPrivacyPolicy: false,
          hasTermsOfService: false,
          hasRedirectNotice: false,
          hasSecurityNotice: false
        };
        
        // 检查登录说明
        if (html.includes('需要登录') || html.includes('Login required')) {
          checks.hasLoginExplanation = true;
        }
        
        // 检查Google标识
        if ((html.includes('Google') && html.includes('登录')) || 
            (html.includes('Google') && html.includes('Sign'))) {
          checks.hasGoogleIdentification = true;
        }
        
        // 检查隐私政策
        if (html.includes('隐私政策') || html.includes('Privacy Policy')) {
          checks.hasPrivacyPolicy = true;
        }
        
        // 检查服务条款
        if (html.includes('服务条款') || html.includes('Terms of Service')) {
          checks.hasTermsOfService = true;
        }
        
        // 检查重定向通知
        if (html.includes('跳转') || html.includes('redirect')) {
          checks.hasRedirectNotice = true;
        }
        
        // 检查安全说明
        if (html.includes('安全') || html.includes('secure')) {
          checks.hasSecurityNotice = true;
        }
        
        console.log(`${checks.hasLoginExplanation ? '✅' : '❌'} 登录说明透明度`);
        console.log(`${checks.hasGoogleIdentification ? '✅' : '❌'} Google服务标识`);
        console.log(`${checks.hasPrivacyPolicy ? '✅' : '❌'} 隐私政策链接`);
        console.log(`${checks.hasTermsOfService ? '✅' : '❌'} 服务条款链接`);
        console.log(`${checks.hasRedirectNotice ? '✅' : '❌'} 重定向通知`);
        console.log(`${checks.hasSecurityNotice ? '✅' : '❌'} 安全说明`);
        
        const score = Object.values(checks).filter(Boolean).length;
        console.log(`\n📊 透明度评分: ${score}/6`);
        
        if (score >= 5) {
          console.log('✅ 透明度合规良好');
        } else if (score >= 3) {
          console.log('⚠️ 透明度需要改进');
        } else {
          console.log('❌ 透明度严重不足');
        }
        
        resolve(checks);
      });
    });
    
    req.on('error', (err) => {
      console.log(`❌ 透明度检查失败: ${err.message}`);
      resolve({});
    });
    
    req.setTimeout(15000, () => {
      req.destroy();
      console.log('❌ 透明度检查超时');
      resolve({});
    });
  });
}

// 2. 检查用户体验欺骗
function checkUXDeception(siteUrl) {
  return new Promise((resolve) => {
    console.log('\n👤 检查用户体验欺骗:');
    
    const req = https.get(siteUrl, (res) => {
      let html = '';
      res.on('data', chunk => html += chunk);
      res.on('end', () => {
        const deceptionIssues = [];
        
        // 检查突然的alert
        if (html.includes('alert(')) {
          deceptionIssues.push('发现alert弹窗代码');
        }
        
        // 检查自动重定向
        if (html.includes('location.href') || html.includes('window.location')) {
          deceptionIssues.push('发现自动重定向代码');
        }
        
        // 检查隐藏元素
        if (html.includes('display:none') && html.includes('iframe')) {
          deceptionIssues.push('发现隐藏iframe');
        }
        
        // 检查误导性文本
        const misleadingPatterns = [
          /病毒/g, /威胁/g, /感染/g, /立即下载/g, /免费下载/g
        ];
        
        misleadingPatterns.forEach(pattern => {
          if (pattern.test(html)) {
            deceptionIssues.push(`发现可能误导的文本: ${pattern.source}`);
          }
        });
        
        if (deceptionIssues.length === 0) {
          console.log('✅ 未发现用户体验欺骗元素');
        } else {
          console.log('⚠️ 发现潜在欺骗元素:');
          deceptionIssues.forEach(issue => console.log(`   - ${issue}`));
        }
        
        resolve(deceptionIssues);
      });
    });
    
    req.on('error', (err) => {
      console.log(`❌ UX欺骗检查失败: ${err.message}`);
      resolve([]);
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      console.log('❌ UX欺骗检查超时');
      resolve([]);
    });
  });
}

// 3. 检查代码合规性
function checkCodeCompliance() {
  console.log('\n💻 检查代码合规性:');
  
  const issues = [];
  
  // 检查主页代码
  const homePagePath = path.join(__dirname, '../src/app/[locale]/(default)/page.tsx');
  if (fs.existsSync(homePagePath)) {
    const content = fs.readFileSync(homePagePath, 'utf8');
    
    // 检查是否还有alert
    if (content.includes('alert(')) {
      issues.push('主页代码中仍有alert调用');
    }
    
    // 检查是否有自动重定向
    if (content.includes('router.push') && content.includes('signin')) {
      issues.push('主页代码中有自动重定向到登录页面');
    }
    
    // 检查是否有登录模态框
    if (content.includes('setShowSignModal')) {
      console.log('✅ 使用登录模态框而非直接重定向');
    } else {
      issues.push('未使用登录模态框');
    }
  }
  
  // 检查登录模态框代码
  const modalPath = path.join(__dirname, '../src/components/sign/modal.tsx');
  if (fs.existsSync(modalPath)) {
    const content = fs.readFileSync(modalPath, 'utf8');
    
    // 检查是否有透明说明
    if (content.includes('sign_in_description')) {
      console.log('✅ 登录模态框包含说明文本');
    } else {
      issues.push('登录模态框缺少说明文本');
    }
    
    // 检查是否有Google标识
    if (content.includes('SiGoogle')) {
      console.log('✅ 明确标识Google登录');
    } else {
      issues.push('缺少Google登录标识');
    }
  }
  
  if (issues.length === 0) {
    console.log('✅ 代码合规性良好');
  } else {
    console.log('⚠️ 发现代码合规问题:');
    issues.forEach(issue => console.log(`   - ${issue}`));
  }
  
  return issues;
}

// 4. 生成合规报告
function generateComplianceReport(transparencyChecks, uxIssues, codeIssues) {
  console.log('\n📋 反欺骗合规报告:');
  console.log('=' * 50);
  
  const transparencyScore = Object.values(transparencyChecks).filter(Boolean).length;
  const totalIssues = uxIssues.length + codeIssues.length;
  
  console.log(`\n📊 评估结果:`);
  console.log(`   透明度评分: ${transparencyScore}/6`);
  console.log(`   发现问题数: ${totalIssues}`);
  
  let complianceLevel = 'FAIL';
  if (transparencyScore >= 5 && totalIssues === 0) {
    complianceLevel = 'EXCELLENT';
  } else if (transparencyScore >= 4 && totalIssues <= 1) {
    complianceLevel = 'GOOD';
  } else if (transparencyScore >= 3 && totalIssues <= 3) {
    complianceLevel = 'FAIR';
  }
  
  console.log(`\n🎯 合规等级: ${complianceLevel}`);
  
  switch (complianceLevel) {
    case 'EXCELLENT':
      console.log('✅ 完全符合谷歌反欺骗标准');
      break;
    case 'GOOD':
      console.log('✅ 基本符合谷歌反欺骗标准');
      break;
    case 'FAIR':
      console.log('⚠️ 部分符合，需要改进');
      break;
    default:
      console.log('❌ 不符合谷歌反欺骗标准，需要重大改进');
  }
  
  console.log('\n💡 改进建议:');
  if (transparencyScore < 5) {
    console.log('1. 增加登录流程的透明度说明');
    console.log('2. 明确标识第三方服务（Google）');
    console.log('3. 提供完整的隐私政策和服务条款');
  }
  
  if (totalIssues > 0) {
    console.log('4. 移除所有突然的重定向和弹窗');
    console.log('5. 确保用户对所有操作都有明确预期');
    console.log('6. 提供清晰的操作说明和结果预期');
  }
  
  return complianceLevel;
}

// 主函数
async function main() {
  try {
    const transparencyChecks = await checkTransparencyCompliance(SITE_URL);
    const uxIssues = await checkUXDeception(SITE_URL);
    const codeIssues = checkCodeCompliance();
    
    const complianceLevel = generateComplianceReport(transparencyChecks, uxIssues, codeIssues);
    
    console.log('\n🚀 下一步行动:');
    if (complianceLevel === 'EXCELLENT' || complianceLevel === 'GOOD') {
      console.log('1. 立即部署当前修复');
      console.log('2. 等待24-48小时让谷歌重新评估');
      console.log('3. 监控谷歌安全浏览状态');
    } else {
      console.log('1. 根据上述建议进行改进');
      console.log('2. 重新运行此检查脚本');
      console.log('3. 确保合规等级达到GOOD以上再部署');
    }
    
    process.exit(complianceLevel === 'FAIL' ? 1 : 0);
    
  } catch (error) {
    console.error('❌ 反欺骗合规检查失败:', error.message);
    process.exit(1);
  }
}

main();
