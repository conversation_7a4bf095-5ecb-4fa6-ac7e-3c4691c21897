import { respData, respErr } from "@/lib/resp";
import { getContacts, getContactsTotal, getContactsStats } from "@/models/contact";
import { isCurrentUserAdmin } from "@/services/admin";

export async function POST(req: Request) {
  try {
    // 检查管理员权限
    const isAdmin = await isCurrentUserAdmin();
    if (!isAdmin) {
      return respErr("无权限访问", 403);
    }

    const { page = 1, limit = 20, status } = await req.json();

    // 获取联系我们列表
    const contacts = await getContacts(page, limit, status);
    const total = await getContactsTotal(status);

    return respData({
      contacts: contacts || [],
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    });
  } catch (e) {
    console.log("get admin contacts failed: ", e);
    return respErr("获取联系我们列表失败");
  }
}

export async function GET(req: Request) {
  try {
    // 检查管理员权限
    const isAdmin = await isCurrentUserAdmin();
    if (!isAdmin) {
      return respErr("无权限访问", 403);
    }

    // 获取联系我们统计数据
    const stats = await getContactsStats();

    return respData(stats);
  } catch (e) {
    console.log("get contacts stats failed: ", e);
    return respErr("获取联系我们统计失败");
  }
}
