"use client";

import Analytics from "@/components/analytics";
import { <PERSON>ache<PERSON><PERSON> } from "@/services/constant";
import { ThemeProvider as NextThemesProvider } from "next-themes";

import type { ThemeProviderProps } from "next-themes";
import { Toaster } from "@/components/ui/sonner";
import { cacheGet } from "@/lib/cache";
import { useAppContext } from "@/contexts/app";
import { useEffect } from "react";

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  return (
    <NextThemesProvider
      attribute="class"
      defaultTheme={process.env.NEXT_PUBLIC_DEFAULT_THEME || "light"}
      enableSystem={false}
      storageKey="theme"
      {...props}
    >
      {children}

      <Toaster position="top-center" richColors />
      <Analytics />
    </NextThemesProvider>
  );
}
